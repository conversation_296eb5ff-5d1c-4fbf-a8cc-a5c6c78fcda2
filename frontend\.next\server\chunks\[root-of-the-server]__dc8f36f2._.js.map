{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/app/api/generate-script/route.ts"], "sourcesContent": ["import { generateObject } from \"ai\";\r\nimport { groq } from \"@ai-sdk/groq\";\r\nimport { z } from \"zod\";\r\nimport { NextResponse } from \"next/server\";\r\n\r\nexport const maxDuration = 30;\r\n\r\nexport async function POST(req: Request) {\r\n  const { prompt }: { prompt: string } = await req.json();\r\n\r\n  const { object } = await generateObject({\r\n    model: groq(\"deepseek-r1-distill-llama-70b\"),\r\n    system: `You are an Educational Content Creator for Manim animations. Transform prompts into 2 scene scripts for mathematical animations. Explain topics clearly for students with no prior knowledge using simple language, analogies, and step-by-step breakdowns.\r\n**Scene Framework:**\r\n- **Scene 1: Title & Hook** - Animated title with visual hook\r\n- **Scene 2: Core Concept** - Define using shapes, arrows, math representations\r\n**Use Only These Manim Elements:**\r\n- Shapes: Circles, Squares, Rectangles, Lines, Arrows, Polygons\r\n- Text: Labels, MathTex (simple LaTeX only)\r\n- Math: NumberLines, Axes, BarCharts, Tables, Coordinates\r\n- Patterns: Step reveals, shape transformations, number sequences, flowcharts, before/after comparisons\r\n**Avoid:** Photos, realistic images, external files, 3D models, complex illustrations\r\n**For Each Scene:**\r\n1. Scene Title\r\n2. Manim Visual Elements\r\n3. Animation Sequence\r\n4. Key Message\r\n5. Transition to next scene\r\n\r\nKeep visuals simple, clean, and easily implementable in Manim.`,\r\n    output: \"array\",\r\n    schema: z.object({\r\n      title: z.string().describe(\"The title of the video scene\"),\r\n      description: z\r\n        .string()\r\n        .describe(\r\n          \"The main description of the scene and all the details in depth\"\r\n        ),\r\n    }),\r\n    prompt,\r\n    schemaName: \"ManimScript\",\r\n    schemaDescription: \"A detailed video script for Manim animation\",\r\n    maxRetries: 3,\r\n  });\r\n\r\n  return NextResponse.json(object);\r\n}\r\n// - **Scene 3: Step-by-Step** - Process via animated transformations\r\n// - **Scene 4: Real-World** - Visual metaphors with geometric shapes\r\n// - **Scene 5: Summary** - Recap with key formula/visual\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AACA;;;;;AAEO,MAAM,cAAc;AAEpB,eAAe,KAAK,GAAY;IACrC,MAAM,EAAE,MAAM,EAAE,GAAuB,MAAM,IAAI,IAAI;IAErD,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,CAAA,GAAA,sJAAA,CAAA,iBAAc,AAAD,EAAE;QACtC,OAAO,CAAA,GAAA,uJAAA,CAAA,OAAI,AAAD,EAAE;QACZ,QAAQ,CAAC;;;;;;;;;;;;;;;;;8DAiBiD,CAAC;QAC3D,QAAQ;QACR,QAAQ,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACf,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;YAC3B,aAAa,mLAAA,CAAA,IAAC,CACX,MAAM,GACN,QAAQ,CACP;QAEN;QACA;QACA,YAAY;QACZ,mBAAmB;QACnB,YAAY;IACd;IAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;AAC3B,EACA,qEAAqE;CACrE,qEAAqE;CACrE,yDAAyD", "debugId": null}}]}