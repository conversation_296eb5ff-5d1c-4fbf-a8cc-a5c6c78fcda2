{"name": "@types/webxr", "version": "0.5.22", "description": "TypeScript definitions for webxr", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/webxr", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "rob<PERSON><PERSON>", "url": "https://github.com/robrohan"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "Raanan<PERSON>", "url": "https://github.com/RaananW"}, {"name": "<PERSON>", "githubUsername": "capnmidnight", "url": "https://github.com/capnmidnight"}, {"name": "<PERSON><PERSON>", "githubUsername": "so<PERSON><PERSON>", "url": "https://github.com/sorskoot"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/webxr"}, "scripts": {}, "dependencies": {}, "peerDependencies": {}, "typesPublisherContentHash": "620e4a94e04f9556eb64925ffb5f49529b2378db636588e9e7836662a65d72de", "typeScriptVersion": "5.1"}