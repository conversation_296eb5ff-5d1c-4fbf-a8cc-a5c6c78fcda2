import os
import uuid
import subprocess
import shutil
import logging
import requests
from datetime import datetime, timezone
from typing import List, Optional
from dotenv import load_dotenv
from fastapi import HTTPException

# Load environment variables like x.py does
dotenv_path = os.path.join(os.path.dirname(__file__), "..", ".env")
load_dotenv(dotenv_path=dotenv_path)

from models.job_models import JobRequest, JobResult, JobStatus, JobType
from services.queue_manager import get_queue_manager
from services.cloudinary_service import get_cloudinary_service
from services.video_rendering_service import get_video_rendering_service
from services.narration_service import get_narration_service
from services.audio_service import get_audio_service
from services.video_merging_service import get_video_merging_service
from config.queue_config import get_queue_config

logger = logging.getLogger(__name__)


class JobProcessor:
    """Processes jobs from the message queue."""

    def __init__(self):
        self.queue_manager = get_queue_manager()
        self.video_rendering_service = get_video_rendering_service()
        self.narration_service = get_narration_service()
        self.audio_service = get_audio_service()
        self.video_merging_service = get_video_merging_service()
        os.makedirs("media", exist_ok=True)

    def process_job(self, job_request: JobRequest) -> JobResult:
        """Process a job based on its type."""
        start_time = datetime.now(timezone.utc)

        try:
            # Update job status to processing
            self.queue_manager.update_job_status(
                job_request.job_id, JobStatus.PROCESSING
            )

            if job_request.job_type == JobType.RENDER:
                result = self._process_render_job(job_request)
            elif job_request.job_type == JobType.BATCH_RENDER:
                result = self._process_batch_render_job(job_request)
            elif job_request.job_type == JobType.TOPIC_RENDER:
                result = self._process_topic_render_job(job_request)
            else:
                raise ValueError(f"Unknown job type: {job_request.job_type}")

            # Calculate processing time
            end_time = datetime.now(timezone.utc)
            processing_time = (end_time - start_time).total_seconds()

            result.processing_time_seconds = processing_time
            result.completed_at = end_time

            # Update job status to completed
            self.queue_manager.update_job_status(
                job_request.job_id, JobStatus.COMPLETED, result=result
            )

            logger.info(
                f"Job {job_request.job_id} completed successfully in {processing_time:.2f}s"
            )
            return result

        except Exception as e:
            error_message = str(e)
            logger.error(f"Job {job_request.job_id} failed: {error_message}")

            # Create failed result
            result = JobResult(
                job_id=job_request.job_id,
                success=False,
                error_message=error_message,
                completed_at=datetime.now(timezone.utc),
            )

            # Update job status to failed
            self.queue_manager.update_job_status(
                job_request.job_id,
                JobStatus.FAILED,
                result=result,
                error_message=error_message,
            )

            return result

    def _process_render_job(self, job_request: JobRequest) -> JobResult:
        """Process a single render job."""
        if not job_request.render_data:
            raise ValueError("Render data is required for render job")

        render_data = job_request.render_data
        job_id = job_request.job_id

        # Create temporary directory
        tmp_dir = os.path.join(os.getcwd(), "tmp")
        os.makedirs(tmp_dir, exist_ok=True)

        try:
            # Write script to file
            script_filename = f"script_{job_id}.py"
            script_path = os.path.join(tmp_dir, script_filename)

            with open(script_path, "w", encoding="utf-8") as f:
                f.write(render_data.script)

            # Determine scene name
            scene = render_data.scene_name or self._get_scene_name(render_data.script)

            # Run manim command
            cmd = f"manim -qh {script_filename} {scene}"

            subprocess.run(
                cmd,
                shell=True,
                cwd=tmp_dir,
                capture_output=True,
                text=True,
                check=True,
                encoding="utf-8",
                errors="replace",
            )

            # Locate rendered video
            output_video_path = self._find_rendered_video(tmp_dir, job_id, scene)

            if not output_video_path or not os.path.exists(output_video_path):
                raise FileNotFoundError("Rendered video not found")

            # Move to final media folder
            final_output_dir = os.path.join("media", job_id)
            os.makedirs(final_output_dir, exist_ok=True)
            final_output_path = os.path.join(final_output_dir, f"{scene}.mp4")
            shutil.move(output_video_path, final_output_path)

            # Upload to Cloudinary and update database if entry_id is provided
            cloudinary_url = None
            logger.info(f"Checking for entry_id in render_data: {render_data.entry_id}")
            if render_data.entry_id:
                logger.info(
                    f"Entry ID found: {render_data.entry_id}, starting Cloudinary upload..."
                )
                try:
                    cloudinary_url = self._upload_and_update_entry(
                        final_output_path, job_id, scene, render_data.entry_id
                    )
                    logger.info(
                        f"Video uploaded to Cloudinary and database updated for entry {render_data.entry_id}"
                    )
                except Exception as e:
                    logger.error(
                        f"Failed to upload to Cloudinary or update database: {e}"
                    )
                    # Don't fail the job if upload fails, just log the error
            else:
                logger.info("No entry_id provided, skipping Cloudinary upload")

            output_urls = [f"/media/{job_id}/{scene}.mp4"]
            if cloudinary_url:
                output_urls.append(cloudinary_url)

            return JobResult(
                job_id=job_id,
                success=True,
                output_urls=output_urls,
            )

        finally:
            # Clean up temporary files
            self._clear_tmp_contents(tmp_dir)

    def _process_batch_render_job(self, job_request: JobRequest) -> JobResult:
        """Process a batch render job."""
        if not job_request.batch_render_data:
            raise ValueError("Batch render data is required for batch render job")

        batch_data = job_request.batch_render_data
        job_id = job_request.job_id

        # Create temporary directory
        tmp_dir = os.path.join(os.getcwd(), "tmp")
        os.makedirs(tmp_dir, exist_ok=True)

        rendered_video_paths = []

        try:
            # Process each script
            for idx, script_req in enumerate(batch_data.scripts):
                script_filename = f"script_{job_id}_{idx}.py"
                script_path = os.path.join(tmp_dir, script_filename)

                with open(script_path, "w", encoding="utf-8") as f:
                    f.write(script_req.script)

                scene = script_req.scene_name or self._get_scene_name(script_req.script)
                cmd = f"manim -qh {script_filename} {scene}"

                subprocess.run(
                    cmd,
                    shell=True,
                    cwd=tmp_dir,
                    capture_output=True,
                    text=True,
                    check=True,
                    encoding="utf-8",
                    errors="replace",
                )

                # Find rendered video
                output_path = self._find_rendered_video(
                    tmp_dir, f"{job_id}_{idx}", scene
                )
                if not output_path or not os.path.exists(output_path):
                    raise FileNotFoundError(
                        f"Rendered video for script {idx + 1} not found"
                    )

                rendered_video_paths.append(output_path)

            # Merge videos using ffmpeg
            merged_output_path = self._merge_videos(
                tmp_dir, rendered_video_paths, job_id
            )

            # Upload to Cloudinary and update database if entry_id is provided
            cloudinary_url = None
            if batch_data.entry_id:
                try:
                    cloudinary_url = self._upload_and_update_entry(
                        merged_output_path, job_id, "merged_output", batch_data.entry_id
                    )
                    logger.info(
                        f"Batch video uploaded to Cloudinary and database updated for entry {batch_data.entry_id}"
                    )
                except Exception as e:
                    logger.error(
                        f"Failed to upload batch video to Cloudinary or update database: {e}"
                    )
                    # Don't fail the job if upload fails, just log the error

            output_urls = [f"/media/{job_id}/merged_output.mp4"]
            if cloudinary_url:
                output_urls.append(cloudinary_url)

            return JobResult(
                job_id=job_id,
                success=True,
                output_urls=output_urls,
            )

        finally:
            # Clean up temporary files
            self._clear_tmp_contents(tmp_dir)

    def _process_topic_render_job(self, job_request: JobRequest) -> JobResult:
        """Process a topic render job with complete x.py functionality."""
        if not job_request.topic_render_data:
            raise ValueError("Topic render data is required for topic render job")

        topic_data = job_request.topic_render_data
        job_id = job_request.job_id

        logger.info(f"🚀 Starting topic render job {job_id}")
        logger.info(f"📋 Topic: {topic_data.topic_name}")
        logger.info(f"📝 Number of scripts to process: {len(topic_data.scripts)}")
        logger.info(f"🆔 Entry ID: {topic_data.entry_id}")

        # Set topic name for cloudinary upload
        self._current_topic_name = topic_data.topic_name

        merged_clips = []

        try:
            # Process each manim script
            for idx, script_data in enumerate(topic_data.scripts):
                logger.info(f"🔄 Processing script {idx + 1}/{len(topic_data.scripts)}")
                logger.info(f"📄 Script description: {script_data.description[:100]}...")

                video_id = f"{job_id}_{idx}"

                # Step 1: Render Manim video with error handling and bugfix integration
                logger.info(f"🎬 Starting manim render for script {idx + 1} (video_id: {video_id})")
                video_path, py_file = self.video_rendering_service.render_manim_video(
                    script_data.manim_code, video_id, max_retries=3
                )

                if not video_path:
                    logger.warning(f"❌ Failed to render video for script {idx + 1} after retries, skipping")
                    continue

                logger.info(f"✅ Successfully rendered video for script {idx + 1}: {video_path}")

                try:
                    # Step 2: Get video duration
                    logger.info(f"📏 Getting video duration for script {idx + 1}")
                    video_duration = self.video_rendering_service.get_media_duration(video_path)
                    if video_duration <= 0:
                        logger.warning(f"⚠️ Invalid video duration for script {idx + 1}, skipping")
                        continue

                    logger.info(f"⏱️ Video duration: {video_duration:.2f} seconds")

                    # Step 3: Generate narration (following x.py pattern)
                    logger.info(f"🗣️ Generating narration for script {idx + 1}")
                    narration = ""
                    if self.narration_service.is_available():
                        narration = self.narration_service.generate_narration(
                            description=script_data.description,
                            topic_name=topic_data.topic_name,
                            duration=int(video_duration)
                        )

                    if not narration:
                        # Fallback to description if narration generation fails
                        narration = script_data.description
                        logger.warning(f"⚠️ Using description as fallback narration for script {idx + 1}")

                    # If still no narration, skip this script
                    if not narration:
                        logger.warning(f"❌ No narration available for script {idx + 1}, skipping")
                        continue

                    logger.info(f"✅ Narration generated ({len(narration)} chars): {narration[:100]}...")

                    # Step 4: Generate TTS audio
                    logger.info(f"🔊 Generating TTS audio for script {idx + 1}")
                    audio_path = self.audio_service.generate_tts_audio(narration)
                    if not audio_path:
                        logger.warning(f"❌ Failed to generate audio for script {idx + 1}, skipping")
                        continue

                    logger.info(f"✅ TTS audio generated: {audio_path}")

                    # Step 5: Merge video and audio
                    logger.info(f"🎞️ Merging video and audio for script {idx + 1}")
                    final_video_path = self.audio_service.merge_audio_video(
                        video_path, audio_path, video_id
                    )

                    if final_video_path:
                        merged_clips.append(final_video_path)
                        logger.info(f"✅ Successfully processed script {idx + 1}: {final_video_path}")
                    else:
                        logger.warning(f"❌ Failed to merge audio/video for script {idx + 1}")

                    # Cleanup audio file
                    logger.info(f"🧹 Cleaning up audio file for script {idx + 1}")
                    self.audio_service.cleanup_audio_files(audio_path)

                finally:
                    # Cleanup temporary files
                    if py_file:
                        self.video_rendering_service.cleanup_temp_files(py_file, video_id)

            # Step 6: Merge all final videos
            if not merged_clips:
                raise ValueError("No videos were successfully processed")

            final_merged_path = self.video_merging_service.merge_multiple_videos(
                merged_clips, topic_data.topic_name
            )

            if not final_merged_path:
                raise ValueError("Failed to merge final videos")

            # Step 7: Save final video as entryID.mp4 and upload to Cloudinary
            cloudinary_url = None
            if topic_data.entry_id:
                try:
                    # Create final video with entryID.mp4 naming
                    final_video_name = f"{topic_data.entry_id}.mp4"
                    final_video_path = os.path.join("media/result", final_video_name)

                    # Copy the merged video to the final location with correct name
                    import shutil
                    shutil.copy2(final_merged_path, final_video_path)
                    logger.info(f"📹 Final video saved as: {final_video_path}")

                    # Upload to Cloudinary with entryID.mp4 filename
                    cloudinary_url = self._upload_and_update_entry(
                        final_video_path,
                        job_id,
                        topic_data.entry_id,  # Use entry_id as scene name for consistency
                        topic_data.entry_id
                    )
                    logger.info(f"☁️ Video uploaded to Cloudinary: {cloudinary_url}")

                    # Step 8: Notify frontend that job is completed
                    self._notify_frontend_completion(topic_data.entry_id, cloudinary_url)

                except Exception as e:
                    logger.error(f"❌ Failed to upload to Cloudinary or update database: {e}")
                    # Don't fail the job if upload fails

            # Prepare output URLs
            output_urls = [f"/media/result/{topic_data.topic_name.lower()}_final_merged.mp4"]
            if cloudinary_url:
                output_urls.append(cloudinary_url)

            # Cleanup intermediate video files
            self.video_merging_service.cleanup_merged_videos(*merged_clips)

            # Step 9: Clean up result directory after successful upload
            if cloudinary_url and final_merged_path:
                try:
                    if os.path.exists(final_merged_path):
                        os.remove(final_merged_path)
                        logger.info(f"Cleaned up final merged video: {final_merged_path}")
                except Exception as e:
                    logger.warning(f"Failed to cleanup final merged video: {e}")

            return JobResult(
                job_id=job_id,
                success=True,
                output_urls=output_urls,
            )

        except Exception as e:
            logger.error(f"Topic render job failed: {e}")
            # Cleanup any intermediate files
            if 'merged_clips' in locals():
                self.video_merging_service.cleanup_merged_videos(*merged_clips)
            raise

    def _get_scene_name(self, script: str) -> str:
        """Extract scene name from script."""
        lines = script.splitlines()
        for line in lines:
            if line.strip().startswith("class ") and "Scene" in line:
                return line.split("(")[0].replace("class ", "").strip()
        return "GenScene"

    def _find_rendered_video(
        self, tmp_dir: str, job_id: str, scene: str
    ) -> Optional[str]:
        """Find the rendered video file."""
        media_root = os.path.join(tmp_dir, "media")

        for root, _, files in os.walk(media_root):
            if f"script_{job_id}" in root:
                for file in files:
                    if (
                        file.endswith(f"{scene}.mp4")
                        and "partial_movie_files" not in root
                    ):
                        return os.path.join(root, file)

        return None

    def _merge_videos(self, tmp_dir: str, video_paths: List[str], job_id: str) -> str:
        """Merge multiple videos into one."""
        # Create list.txt for ffmpeg concat
        concat_list_path = os.path.join(tmp_dir, "list.txt")
        with open(concat_list_path, "w", encoding="utf-8") as f:
            for path in video_paths:
                f.write(f"file '{path}'\n")

        # Create final output directory
        final_output_dir = os.path.join("media", job_id)
        os.makedirs(final_output_dir, exist_ok=True)
        merged_output_path = os.path.join(final_output_dir, "merged_output.mp4")

        # Run ffmpeg merge command
        subprocess.run(
            f"ffmpeg -f concat -safe 0 -i list.txt -c copy merged_output.mp4",
            shell=True,
            cwd=tmp_dir,
            capture_output=True,
            text=True,
            check=True,
        )

        # Move merged video to final location
        shutil.move(os.path.join(tmp_dir, "merged_output.mp4"), merged_output_path)

        return merged_output_path

    def _upload_and_update_entry(
        self, video_path: str, job_id: str, scene_name: str, entry_id: str
    ) -> str:
        """Upload video to Cloudinary and update database entry."""
        try:
            # Upload to Cloudinary
            cloudinary_service = get_cloudinary_service()

            if not cloudinary_service.is_configured():
                logger.warning("Cloudinary not configured, skipping upload")
                return ""

            # Use topic_video upload for topic render jobs, regular upload for others
            if hasattr(self, '_current_topic_name') and self._current_topic_name:
                cloudinary_url = cloudinary_service.upload_topic_video(
                    video_path, self._current_topic_name, entry_id
                )
            else:
                cloudinary_url = cloudinary_service.upload_video_from_job(
                    video_path, job_id, scene_name
                )

            # Update database entry
            config = get_queue_config()
            if config.backend_api_base_url:
                self._update_database_entry(entry_id, cloudinary_url)
            else:
                logger.warning(
                    "Backend API base URL not configured, skipping database update"
                )

            return cloudinary_url

        except Exception as e:
            logger.error(f"Failed to upload and update entry: {e}")
            raise

    def _update_database_entry(self, entry_id: str, video_url: str):
        """Update database entry with video URL."""
        try:
            config = get_queue_config()
            api_url = f"{config.backend_api_base_url}/api/update-video"

            payload = {"id": entry_id, "videoUrl": video_url}

            response = requests.post(
                api_url,
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=30,
            )

            response.raise_for_status()
            logger.info(
                f"Successfully updated database entry {entry_id} with video URL"
            )

        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to update database entry {entry_id}: {e}")
            raise

    def _notify_frontend_completion(self, entry_id: str, video_url: str):
        """Notify frontend that video processing is completed."""
        try:
            config = get_queue_config()
            if not config.backend_api_base_url:
                logger.warning("Backend API base URL not configured, skipping frontend notification")
                return

            api_url = f"{config.backend_api_base_url}/api/completed"

            payload = {
                "entryId": entry_id,
                "videoUrl": video_url,
                "status": "completed"
            }

            response = requests.post(
                api_url,
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=30,
            )

            response.raise_for_status()
            logger.info(f"Successfully notified frontend of completion for entry {entry_id}")

        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to notify frontend of completion for entry {entry_id}: {e}")
            # Don't raise error as this is not critical for job completion

    def _clear_tmp_contents(self, tmp_dir: str):
        """Clean up temporary directory contents."""
        try:
            for item in os.listdir(tmp_dir):
                item_path = os.path.join(tmp_dir, item)
                if os.path.isfile(item_path):
                    os.remove(item_path)
                elif os.path.isdir(item_path):
                    shutil.rmtree(item_path)
        except Exception as e:
            logger.warning(f"Failed to clean tmp directory: {e}")


# Global job processor instance
_job_processor: Optional[JobProcessor] = None


def get_job_processor() -> JobProcessor:
    """Get the global job processor instance."""
    global _job_processor
    if _job_processor is None:
        _job_processor = JobProcessor()
    return _job_processor
