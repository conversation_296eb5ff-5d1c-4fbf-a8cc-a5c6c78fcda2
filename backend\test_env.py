#!/usr/bin/env python3
"""
Test script to verify environment variables are loaded correctly.
"""

import os
from dotenv import load_dotenv

# Load environment variables like x.py does
dotenv_path = os.path.join(os.path.dirname(__file__), ".env")
load_dotenv(dotenv_path=dotenv_path)

def test_env_vars():
    """Test that all required environment variables are loaded."""
    
    required_vars = [
        "GOOGLE_API_KEY",
        "TTS_ENDPOINT", 
        "CLOUDINARY_CLOUD_NAME",
        "CLOUDINARY_API_KEY",
        "CLOUDINARY_API_SECRET",
        "BACKEND_API_BASE_URL",
        "QSTASH_TOKEN",
        "WORKER_BASE_URL",
        "WORKER_SECRET"
    ]
    
    print("🔍 Testing environment variables...")
    print("=" * 50)
    
    all_good = True
    
    for var in required_vars:
        value = os.getenv(var)
        if value:
            # Mask sensitive values
            if "KEY" in var or "SECRET" in var or "TOKEN" in var:
                display_value = f"{value[:10]}..." if len(value) > 10 else "***"
            else:
                display_value = value
            print(f"✅ {var}: {display_value}")
        else:
            print(f"❌ {var}: NOT SET")
            all_good = False
    
    print("=" * 50)
    
    if all_good:
        print("🎉 All environment variables are properly loaded!")
        return True
    else:
        print("⚠️  Some environment variables are missing!")
        return False

if __name__ == "__main__":
    test_env_vars()
