{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/components/mvpblocks/TaskProgressSidebar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { AnimatePresence, motion } from \"framer-motion\";\r\nimport { Accordion } from \"@/components/ui/accordion\";\r\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\r\nimport { CheckCircle2, Clock, AlertCircle, Loader } from \"lucide-react\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\ninterface Task {\r\n  id: string;\r\n  name: string;\r\n  status: \"pending\" | \"in-progress\" | \"completed\" | \"failed\";\r\n}\r\n\r\ninterface TaskProgressSidebarProps {\r\n  isGenerating: boolean;\r\n  tasks: Task[];\r\n  currentGeneratingScene?: string | null;\r\n}\r\n\r\nexport default function TaskProgressSidebar({\r\n  isGenerating = true,\r\n  tasks = [\r\n    { id: \"1\", name: \"Download assets\", status: \"completed\" },\r\n    { id: \"2\", name: \"Generate script\", status: \"completed\" },\r\n    { id: \"3\", name: \"Render video\", status: \"in-progress\" },\r\n    { id: \"4\", name: \"Upload to cloud\", status: \"pending\" },\r\n    { id: \"5\", name: \"Notify user\", status: \"pending\" },\r\n  ],\r\n  currentGeneratingScene = \"Scene 2: Rendering animation\",\r\n}: TaskProgressSidebarProps) {\r\n  const getStatusIcon = (status: Task[\"status\"]) => {\r\n    switch (status) {\r\n      case \"completed\":\r\n        return <CheckCircle2 className=\"h-4 w-4 text-green-500\" />;\r\n      case \"in-progress\":\r\n        return <Loader className=\"h-4 w-4 text-cyan-500 animate-spin\" />;\r\n      case \"pending\":\r\n        return <Clock className=\"h-4 w-4 text-yellow-500\" />;\r\n      case \"failed\":\r\n        return <AlertCircle className=\"h-4 w-4 text-destructive\" />;\r\n      default:\r\n        return <Clock className=\"h-4 w-4 text-muted-foreground\" />;\r\n    }\r\n  };\r\n\r\n  const getStatusColor = (status: Task[\"status\"]) => {\r\n    switch (status) {\r\n      case \"completed\":\r\n        return \"bg-gradient-to-r via-green-500/10 border-green-500/20\";\r\n      case \"in-progress\":\r\n        return \"bg-gradient-to-r via-cyan-400/20 border-cyan-500/20\";\r\n      case \"pending\":\r\n        return \"bg-gradient-to-r via-yellow-500/10 border-yellow-500/20\";\r\n      case \"failed\":\r\n        return \"bg-gradient-to-r via-rose-500/10 border-rose-500/20\";\r\n      default:\r\n        return \"bg-secondary/30\";\r\n    }\r\n  };\r\n\r\n  if (!isGenerating) return null;\r\n\r\n  return (\r\n    <AnimatePresence>\r\n      <motion.div\r\n        className=\"col-span-1 lg:col-span-2 h-full flex flex-col border-dashed bg-background\"\r\n        initial={{ opacity: 0, x: -20 }}\r\n        animate={{ opacity: 1, x: 0 }}\r\n        exit={{ opacity: 0, x: -20 }}\r\n        transition={{ duration: 0.3, ease: \"easeOut\" }}\r\n      >\r\n        <div className=\"px-6 py-4 border-b border-dashed bg-background\">\r\n          <h3 className=\"text-lg\">Task Progress</h3>\r\n        </div>\r\n\r\n        <div className=\"flex-1 px-6 pt-4 pb-10\">\r\n          <div className=\"space-y-6\">\r\n            {/* Current Tasks */}\r\n            <div className=\"w-full\">\r\n              <div className=\"flex items-center mb-4\">\r\n                <div className=\"w-1 h-6 bg-gradient-to-b from-primary to-primary/50 rounded-full mr-3\"></div>\r\n                <h4 className=\"text-sm font-semibold text-muted-foreground uppercase tracking-wide\">\r\n                  Current Progress\r\n                </h4>\r\n              </div>\r\n              <div className=\"space-y-2\">\r\n                {tasks.map((task, index) => (\r\n                  <motion.div\r\n                    key={task.id}\r\n                    initial={{ opacity: 0, y: 10 }}\r\n                    animate={{ opacity: 1, y: 0 }}\r\n                    transition={{ delay: index * 0.1, duration: 0.3 }}\r\n                    className={cn(\r\n                      \"relative overflow- w-full rounded-lg border cursor-pointer hover:saturate-150 hover:brightness-150 transition-all duration-300\",\r\n                      getStatusColor(task.status),\r\n                      task.status === \"in-progress\" && \"animate-bg-pan\"\r\n                    )}\r\n                  >\r\n                    <div className=\"flex items-center space-x-3 relative p-4 pr-2\">\r\n                      <div\r\n                        className={`p-2 rounded-lg transition-all duration-200 ${\r\n                          task.status === \"in-progress\"\r\n                            ? \"bg-primary/10 inset-shadow-xs inset-shadow-primary/30\"\r\n                            : task.status === \"completed\"\r\n                            ? \"bg-green-500/10 inset-shadow-xs inset-shadow-green-600/30\"\r\n                            : task.status === \"failed\"\r\n                            ? \"bg-rose-500/20\"\r\n                            : \"bg-yellow-500/20\"\r\n                        }`}\r\n                      >\r\n                        {getStatusIcon(task.status)}\r\n                      </div>\r\n                      <div className=\"flex-1 min-w-0\">\r\n                        <h4 className=\"font-medium text-xs\">{task.name}</h4>\r\n                        {task.status === \"in-progress\" &&\r\n                          currentGeneratingScene && (\r\n                            <motion.p\r\n                              initial={{ opacity: 0 }}\r\n                              animate={{ opacity: 1 }}\r\n                              className=\"text-xs text-muted-foreground mt-1 truncate\"\r\n                            >\r\n                              {currentGeneratingScene}\r\n                            </motion.p>\r\n                          )}\r\n                        {task.status === \"in-progress\" && (\r\n                          <div className=\"mt-2 w-full bg-secondary rounded-full h-1.5 relative\">\r\n                            <div className=\"bg-gradient-to-r from-teal-300 via-blue-500 to-violet-400 h-1.5 rounded-full animate-pulse w-3/4\"></div>\r\n                            <div className=\"bg-gradient-to-r from-teal-300 via-blue-500 to-violet-400 h-1.5 rounded-full animate-pulse w-3/4 absolute top-0 left-0 blur animate-pulse translate-y-0.5\"></div>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                      <div\r\n                        className={`text-[10px] font-medium px-2.5 py-1 rounded-full transition-all duration-200 ${\r\n                          task.status === \"completed\"\r\n                            ? \"bg-green-500/10 text-green-500 border border-green-500/30\"\r\n                            : task.status === \"in-progress\"\r\n                            ? \"bg-cyan-500/10 text-cyan-500 border border-cyan-500/30\"\r\n                            : task.status === \"failed\"\r\n                            ? \"bg-rose-500/10 text-rose-500 border border-rose-500/30\"\r\n                            : \"bg-yellow-500/10 text-yellow-500 border border-yellow-500/30\"\r\n                        }`}\r\n                      >\r\n                        {task.status}\r\n                      </div>\r\n                    </div>\r\n                  </motion.div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n\r\n            {/* Progress Summary */}\r\n            <div className=\"p-4 bg-gradient-to-br from-secondary/30 to-secondary/10 rounded-xl border border-border/30\">\r\n              <div className=\"flex items-center justify-between mb-3\">\r\n                <span className=\"text-sm font-medium text-muted-foreground\">\r\n                  Overall Progress\r\n                </span>\r\n                <span className=\"text-sm font-bold\">\r\n                  {tasks.filter((t) => t.status === \"completed\").length}/\r\n                  {tasks.length}\r\n                </span>\r\n              </div>\r\n              <div className=\"w-full rounded-full h-2 relative\">\r\n                <div\r\n                  className=\"bg-gradient-to-r from-purple-500 to-violet-500 via-fuchsia-400  h-2 rounded-full transition-all duration-500\"\r\n                  style={{\r\n                    width: `${\r\n                      (tasks.filter((t) => t.status === \"completed\").length /\r\n                        tasks.length) *\r\n                      100\r\n                    }%`,\r\n                  }}\r\n                ></div>\r\n                <div\r\n                  className={cn(\r\n                    \"bg-gradient-to-r from-purple-500 to-violet-500 via-fuchsia-400 h-2 rounded-full transition-all duration-500 absolute top-0 left-0 blur animate-pulse translate-y-0.5\"\r\n                  )}\r\n                  style={{\r\n                    width: `${\r\n                      (tasks.filter((t) => t.status === \"completed\").length /\r\n                        tasks.length) *\r\n                      100\r\n                    }%`,\r\n                  }}\r\n                ></div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </motion.div>\r\n    </AnimatePresence>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAGA;AAAA;AAAA;AAAA;AACA;AANA;;;;;AAoBe,SAAS,oBAAoB,EAC1C,eAAe,IAAI,EACnB,QAAQ;IACN;QAAE,IAAI;QAAK,MAAM;QAAmB,QAAQ;IAAY;IACxD;QAAE,IAAI;QAAK,MAAM;QAAmB,QAAQ;IAAY;IACxD;QAAE,IAAI;QAAK,MAAM;QAAgB,QAAQ;IAAc;IACvD;QAAE,IAAI;QAAK,MAAM;QAAmB,QAAQ;IAAU;IACtD;QAAE,IAAI;QAAK,MAAM;QAAe,QAAQ;IAAU;CACnD,EACD,yBAAyB,8BAA8B,EAC9B;IACzB,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,wNAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;YACjC,KAAK;gBACH,qBAAO,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3B,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;QAC5B;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,CAAC,cAAc,OAAO;IAE1B,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACd,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,SAAS;gBAAE,SAAS;gBAAG,GAAG,CAAC;YAAG;YAC9B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,MAAM;gBAAE,SAAS;gBAAG,GAAG,CAAC;YAAG;YAC3B,YAAY;gBAAE,UAAU;gBAAK,MAAM;YAAU;;8BAE7C,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAG,WAAU;kCAAU;;;;;;;;;;;8BAG1B,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAG,WAAU;0DAAsE;;;;;;;;;;;;kDAItF,6LAAC;wCAAI,WAAU;kDACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,OAAO,QAAQ;oDAAK,UAAU;gDAAI;gDAChD,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kIACA,eAAe,KAAK,MAAM,GAC1B,KAAK,MAAM,KAAK,iBAAiB;0DAGnC,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,WAAW,CAAC,2CAA2C,EACrD,KAAK,MAAM,KAAK,gBACZ,0DACA,KAAK,MAAM,KAAK,cAChB,8DACA,KAAK,MAAM,KAAK,WAChB,mBACA,oBACJ;sEAED,cAAc,KAAK,MAAM;;;;;;sEAE5B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAAuB,KAAK,IAAI;;;;;;gEAC7C,KAAK,MAAM,KAAK,iBACf,wCACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oEACP,SAAS;wEAAE,SAAS;oEAAE;oEACtB,SAAS;wEAAE,SAAS;oEAAE;oEACtB,WAAU;8EAET;;;;;;gEAGN,KAAK,MAAM,KAAK,+BACf,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;;;;;sFACf,6LAAC;4EAAI,WAAU;;;;;;;;;;;;;;;;;;sEAIrB,6LAAC;4DACC,WAAW,CAAC,6EAA6E,EACvF,KAAK,MAAM,KAAK,cACZ,8DACA,KAAK,MAAM,KAAK,gBAChB,2DACA,KAAK,MAAM,KAAK,WAChB,2DACA,gEACJ;sEAED,KAAK,MAAM;;;;;;;;;;;;+CAtDX,KAAK,EAAE;;;;;;;;;;;;;;;;0CA+DpB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAA4C;;;;;;0DAG5D,6LAAC;gDAAK,WAAU;;oDACb,MAAM,MAAM,CAAC,CAAC,IAAM,EAAE,MAAM,KAAK,aAAa,MAAM;oDAAC;oDACrD,MAAM,MAAM;;;;;;;;;;;;;kDAGjB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,WAAU;gDACV,OAAO;oDACL,OAAO,GACL,AAAC,MAAM,MAAM,CAAC,CAAC,IAAM,EAAE,MAAM,KAAK,aAAa,MAAM,GACnD,MAAM,MAAM,GACd,IACD,CAAC,CAAC;gDACL;;;;;;0DAEF,6LAAC;gDACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV;gDAEF,OAAO;oDACL,OAAO,GACL,AAAC,MAAM,MAAM,CAAC,CAAC,IAAM,EAAE,MAAM,KAAK,aAAa,MAAM,GACnD,MAAM,MAAM,GACd,IACD,CAAC,CAAC;gDACL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlB;KA5KwB", "debugId": null}}, {"offset": {"line": 395, "column": 0}, "map": {"version": 3, "file": "circle-alert.js", "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/node_modules/lucide-react/src/icons/circle-alert.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['line', { x1: '12', x2: '12', y1: '8', y2: '12', key: '1pkeuh' }],\n  ['line', { x1: '12', x2: '12.01', y1: '16', y2: '16', key: '4dfq90' }],\n];\n\n/**\n * @component @name CircleAlert\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMiIgeTE9IjgiIHkyPSIxMiIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMi4wMSIgeTE9IjE2IiB5Mj0iMTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/circle-alert\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleAlert = createLucideIcon('circle-alert', __iconNode);\n\nexport default CircleAlert;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACjE;QAAC,MAAA,CAAQ;QAAA,CAAA;YAAE,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,IAAM,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACvE;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}