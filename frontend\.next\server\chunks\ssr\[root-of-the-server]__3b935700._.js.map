{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/components/ui/sonner.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useTheme } from \"next-themes\"\r\nimport { Toaster as Son<PERSON>, ToasterP<PERSON> } from \"sonner\"\r\n\r\nconst Toaster = ({ ...props }: ToasterProps) => {\r\n  const { theme = \"system\" } = useTheme()\r\n\r\n  return (\r\n    <Sonner\r\n      theme={theme as ToasterProps[\"theme\"]}\r\n      className=\"toaster group\"\r\n      style={\r\n        {\r\n          \"--normal-bg\": \"var(--popover)\",\r\n          \"--normal-text\": \"var(--popover-foreground)\",\r\n          \"--normal-border\": \"var(--border)\",\r\n        } as React.CSSProperties\r\n      }\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Toaster }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,UAAU,CAAC,EAAE,GAAG,OAAqB;IACzC,MAAM,EAAE,QAAQ,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IAEpC,qBACE,8OAAC,wIAAA,CAAA,UAAM;QACL,OAAO;QACP,WAAU;QACV,OACE;YACE,eAAe;YACf,iBAAiB;YACjB,mBAAmB;QACrB;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 49, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/contexts/GenerationContext.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { createContext, useContext, useState, useCallback, useEffect } from 'react';\nimport { toast } from 'sonner';\n\ninterface GenerationState {\n  isGenerating: boolean;\n  currentEntryId: string | null;\n  videoJobId: string | null;\n  queuePosition: number | null;\n  isCompleted: boolean;\n  completedVideoUrl: string | null;\n}\n\ninterface GenerationContextType {\n  state: GenerationState;\n  startGeneration: (entryId: string, jobId: string, queuePosition?: number) => void;\n  stopGeneration: () => void;\n  updateQueuePosition: (position: number) => void;\n  markCompleted: (videoUrl: string) => void;\n  resetState: () => void;\n}\n\nconst GenerationContext = createContext<GenerationContextType | undefined>(undefined);\n\nconst initialState: GenerationState = {\n  isGenerating: false,\n  currentEntryId: null,\n  videoJobId: null,\n  queuePosition: null,\n  isCompleted: false,\n  completedVideoUrl: null,\n};\n\nexport function GenerationProvider({ children }: { children: React.ReactNode }) {\n  const [state, setState] = useState<GenerationState>(initialState);\n\n  // Listen for completion notifications\n  useEffect(() => {\n    const handleCompletion = (event: CustomEvent) => {\n      const { entryId, videoUrl } = event.detail;\n      \n      if (state.currentEntryId === entryId) {\n        setState(prev => ({\n          ...prev,\n          isGenerating: false,\n          isCompleted: true,\n          completedVideoUrl: videoUrl,\n        }));\n        \n        toast.success(\"Video generation completed!\", {\n          description: \"Your video is ready to view in the library.\",\n          action: {\n            label: \"View Library\",\n            onClick: () => window.location.href = \"/library\"\n          }\n        });\n      }\n    };\n\n    window.addEventListener('videoCompleted', handleCompletion as EventListener);\n    \n    return () => {\n      window.removeEventListener('videoCompleted', handleCompletion as EventListener);\n    };\n  }, [state.currentEntryId]);\n\n  const startGeneration = useCallback((entryId: string, jobId: string, queuePosition?: number) => {\n    setState({\n      isGenerating: true,\n      currentEntryId: entryId,\n      videoJobId: jobId,\n      queuePosition: queuePosition || null,\n      isCompleted: false,\n      completedVideoUrl: null,\n    });\n  }, []);\n\n  const stopGeneration = useCallback(() => {\n    setState(prev => ({\n      ...prev,\n      isGenerating: false,\n    }));\n  }, []);\n\n  const updateQueuePosition = useCallback((position: number) => {\n    setState(prev => ({\n      ...prev,\n      queuePosition: position,\n    }));\n  }, []);\n\n  const markCompleted = useCallback((videoUrl: string) => {\n    setState(prev => ({\n      ...prev,\n      isGenerating: false,\n      isCompleted: true,\n      completedVideoUrl: videoUrl,\n    }));\n  }, []);\n\n  const resetState = useCallback(() => {\n    setState(initialState);\n  }, []);\n\n  const value: GenerationContextType = {\n    state,\n    startGeneration,\n    stopGeneration,\n    updateQueuePosition,\n    markCompleted,\n    resetState,\n  };\n\n  return (\n    <GenerationContext.Provider value={value}>\n      {children}\n    </GenerationContext.Provider>\n  );\n}\n\nexport function useGeneration() {\n  const context = useContext(GenerationContext);\n  if (context === undefined) {\n    throw new Error('useGeneration must be used within a GenerationProvider');\n  }\n  return context;\n}\n\n// Helper function to dispatch completion events\nexport function dispatchVideoCompletion(entryId: string, videoUrl: string) {\n  const event = new CustomEvent('videoCompleted', {\n    detail: { entryId, videoUrl }\n  });\n  window.dispatchEvent(event);\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAHA;;;;AAuBA,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAqC;AAE3E,MAAM,eAAgC;IACpC,cAAc;IACd,gBAAgB;IAChB,YAAY;IACZ,eAAe;IACf,aAAa;IACb,mBAAmB;AACrB;AAEO,SAAS,mBAAmB,EAAE,QAAQ,EAAiC;IAC5E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAEpD,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB,CAAC;YACxB,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,MAAM,MAAM;YAE1C,IAAI,MAAM,cAAc,KAAK,SAAS;gBACpC,SAAS,CAAA,OAAQ,CAAC;wBAChB,GAAG,IAAI;wBACP,cAAc;wBACd,aAAa;wBACb,mBAAmB;oBACrB,CAAC;gBAED,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,+BAA+B;oBAC3C,aAAa;oBACb,QAAQ;wBACN,OAAO;wBACP,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;oBACxC;gBACF;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,kBAAkB;QAE1C,OAAO;YACL,OAAO,mBAAmB,CAAC,kBAAkB;QAC/C;IACF,GAAG;QAAC,MAAM,cAAc;KAAC;IAEzB,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,SAAiB,OAAe;QACnE,SAAS;YACP,cAAc;YACd,gBAAgB;YAChB,YAAY;YACZ,eAAe,iBAAiB;YAChC,aAAa;YACb,mBAAmB;QACrB;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,SAAS,CAAA,OAAQ,CAAC;gBAChB,GAAG,IAAI;gBACP,cAAc;YAChB,CAAC;IACH,GAAG,EAAE;IAEL,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACvC,SAAS,CAAA,OAAQ,CAAC;gBAChB,GAAG,IAAI;gBACP,eAAe;YACjB,CAAC;IACH,GAAG,EAAE;IAEL,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,SAAS,CAAA,OAAQ,CAAC;gBAChB,GAAG,IAAI;gBACP,cAAc;gBACd,aAAa;gBACb,mBAAmB;YACrB,CAAC;IACH,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,SAAS;IACX,GAAG,EAAE;IAEL,MAAM,QAA+B;QACnC;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,kBAAkB,QAAQ;QAAC,OAAO;kBAChC;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAGO,SAAS,wBAAwB,OAAe,EAAE,QAAgB;IACvE,MAAM,QAAQ,IAAI,YAAY,kBAAkB;QAC9C,QAAQ;YAAE;YAAS;QAAS;IAC9B;IACA,OAAO,aAAa,CAAC;AACvB", "debugId": null}}]}