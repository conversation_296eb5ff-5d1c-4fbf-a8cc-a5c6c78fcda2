"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import InputSection from "./InputSection";
import VideoConfirmationDialog from "./VideoConfirmationDialog";
import { type Entries } from "@prisma/client";
import { useAuthUser } from "@/hooks/useAuthUser";
import dynamic from "next/dynamic";

const ContentDisplayPanel = dynamic(() => import("./ContentDisplayPanel"), {
  ssr: false,
});

const TaskProgressSidebar = dynamic(() => import("./TaskProgressSidebar"), {
  ssr: false,
});

interface ScriptItem {
  title: string;
  description: string;
  code?: string;
}

interface Task {
  id: string;
  name: string;
  status: "pending" | "in-progress" | "completed" | "failed";
}

const BACKEND_URL =
  process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:8000";

export default function Bolt() {
  const { user } = useAuthUser();
  const abortControllerRef = useRef<AbortController | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [showGenerationUI, setShowGenerationUI] = useState(false);
  const [tasks, setTasks] = useState<Task[]>([]);
  const [currentScripts, setCurrentScripts] = useState<ScriptItem[]>([]);
  const [showConfirmationDialog, setShowConfirmationDialog] = useState(false);
  const [videoJobId, setVideoJobId] = useState<string | null>(null);
  const [queuePosition, setQueuePosition] = useState<number | null>(null);
  const [, setQuizId] = useState<string | null>(null);
  const [mainTheme, setMainTheme] = useState<string>("");

  useEffect(() => {
    if (typeof window !== "undefined") {
      const savedPrompt = localStorage.getItem("currentPrompt") || "";
      setMainTheme(savedPrompt);
    }
  }, []);

  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  const generateScriptWithFetch = useCallback(async (prompt: string) => {
    try {
      setIsGenerating(true);

      abortControllerRef.current = new AbortController();

      const response = await fetch("/api/generate-script", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ prompt }),
        signal: abortControllerRef.current.signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const generatedScripts: ScriptItem[] = await response.json();
      setCurrentScripts(generatedScripts);

      setTasks((prevTasks) =>
        prevTasks.map((task) =>
          task.id === "1"
            ? { ...task, status: "completed" }
            : task.id === "2"
            ? { ...task, status: "completed" }
            : task
        )
      );

      // Wait for quiz generation and get the quizId
      console.log("🧠 Starting quiz generation...");
      const generatedQuizId = await generateQuizWithFetch(
        generatedScripts.map((script) => script.title),
        generatedScripts.map((script) => script.description)
      );

      console.log(`✅ Quiz generation completed. Quiz ID: ${generatedQuizId}`);

      // Pass the quizId to generateManimCodes
      console.log("🎨 Starting manim code generation...");
      await generateManimCodes(generatedScripts, generatedQuizId, prompt);
    } catch (error) {
      if (error instanceof Error && error.name === "AbortError") {
        console.log("Request was aborted");
        return;
      }

      setTasks((prevTasks) =>
        prevTasks.map((task) =>
          task.status === "in-progress"
            ? {
                ...task,
                status: "failed",
                name: `${task.name} (Error: ${
                  error instanceof Error ? error.message : String(error)
                })`,
              }
            : task
        )
      );
    }
  }, []);

  const generateQuizWithFetch = async (
    title: string[],
    description: string[]
  ): Promise<string | null> => {
    try {
      const combinedTitle = title.map((t) => t.trim()).join(",");
      const combinedDescription = description.map((d) => d.trim()).join("\n");
      const userId = user.id;

      const response = await fetch("/api/ai-quiz", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          title: combinedTitle,
          content: combinedDescription,
          userId: userId,
        }),
        signal: abortControllerRef.current?.signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const quizData = (await response.json()) as Entries;
      setQuizId(quizData.id);
      return quizData.id; // Return the quizId
    } catch (error) {
      if (error instanceof Error && error.name === "AbortError") {
        return null;
      }

      console.error("Error generating quiz:", error);
      return null;
    }
  };

  const generateManimCodeWithFetch = useCallback(
    async (
      title: string,
      description: string,
      currentPrompt: string
    ): Promise<string | null> => {
      try {
        console.log(`🎨 Generating manim code for: ${title}`);

        const response = await fetch("/api/manim", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            schema: { title, description },
            mainTheme: currentPrompt,
          }),
          signal: abortControllerRef.current?.signal,
        });

        if (!response.ok) {
          console.error(`❌ Manim API error for ${title}: ${response.status}`);
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        const code = data.code;

        console.log(`✅ Manim code generated for ${title}: ${code ? 'Success' : 'No code returned'}`);

        if (!code) {
          console.warn(`⚠️ No code returned for ${title}`);
          return null;
        }

        setCurrentScripts((prev) =>
          prev.map((script) =>
            script.title === title ? { ...script, code: code } : script
          )
        );

        setTasks((prevTasks) =>
          prevTasks.map((task) =>
            task.id === `manim-${title}`
              ? { ...task, status: "completed" }
              : task
          )
        );

        return code;
      } catch (error) {
        if (error instanceof Error && error.name === "AbortError") {
          return null;
        }

        setTasks((prevTasks) =>
          prevTasks.map((task) =>
            task.id === `manim-${title}`
              ? {
                  ...task,
                  status: "failed",
                  name: `${task.name} (Error: ${
                    error instanceof Error ? error.message : String(error)
                  })`,
                }
              : task
          )
        );
        return null;
      }
    },
    [] // Remove mainTheme dependency since we're passing it as parameter
  );

  const renderVideoWithBackend = useCallback(
    async (scripts: ScriptItem[], quizId: string) => {
      try {
        console.log("🎬 Starting renderVideoWithBackend function");
        console.log(`📝 Received ${scripts.length} scripts`);
        console.log(`🆔 Quiz ID: ${quizId}`);

        const manimCodes = scripts
          .filter((script) => script.code)
          .map((script) => script.code!);

        console.log(`✅ Filtered to ${manimCodes.length} scripts with valid code`);

        if (manimCodes.length === 0) {
          console.error("❌ No valid scripts to render");
          throw new Error("No valid scripts to render");
        }

        if (!quizId) {
          console.error("❌ Quiz ID not available");
          throw new Error("Quiz ID not available");
        }

        // Prepare the batch render payload with the new format
        const scriptsData = scripts
          .filter((script) => script.code)
          .map((script) => ({
            manim_code: script.code!,
            description: script.description,
          }));

        const renderPayload = {
          topicName: mainTheme || "Generated Topic",
          entryId: quizId,
          scripts: scriptsData,
          priority: 0,
        };

        // Mark entry as generating in database
        try {
          await fetch("/api/start-generation", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ entryId: quizId }),
          });
          console.log("✅ Entry marked as generating in database");
        } catch (dbError) {
          console.error("⚠️ Failed to mark entry as generating:", dbError);
          // Continue with video generation even if DB update fails
        }

        console.log("Sending batch render request:", renderPayload);

        const response = await fetch(`${BACKEND_URL}/batch_render`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(renderPayload),
          signal: abortControllerRef.current?.signal,
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`Backend error: ${response.status} - ${errorText}`);
        }

        const result = await response.json();
        console.log("Batch render response:", result);

        setVideoJobId(result.job_id);
        setQueuePosition(result.queue_position);

        console.log(`🎯 Video job queued successfully!`);
        console.log(`🆔 Job ID: ${result.job_id}`);
        console.log(`📍 Queue Position: ${result.queue_position}`);

        setTasks((prevTasks) => [
          ...prevTasks,
          {
            id: "4",
            name: "Rendering Video",
            status: "completed",
          },
        ]);

        // Show confirmation dialog after a short delay
        setTimeout(() => {
          setIsGenerating(false);
          console.log("🎉 Showing confirmation dialog");
          setShowConfirmationDialog(true);
        }, 500);
      } catch (error) {
        if (error instanceof Error && error.name === "AbortError") {
          return;
        }

        console.error("Error rendering video:", error);
        setTasks((prevTasks) => [
          ...prevTasks,
          {
            id: "4",
            name: `Video Rendering Failed: ${
              error instanceof Error ? error.message : String(error)
            }`,
            status: "failed",
          },
        ]);
      }
    },
    []
  );

  const generateManimCodes = useCallback(
    async (
      scripts: ScriptItem[],
      quizId: string | null,
      currentPrompt: string
    ) => {
      console.log("🎨 generateManimCodes function called");
      console.log(`📝 Scripts to process: ${scripts.length}`);
      console.log(`🆔 Quiz ID: ${quizId}`);
      console.log(`💭 Current prompt: ${currentPrompt}`);

      const manimTasks: Task[] = scripts.map((script) => ({
        id: `manim-${script.title}`,
        name: `Generating: ${script.title}`,
        status: "pending",
      }));

      setTasks((prevTasks) => [
        ...prevTasks.filter((t) => t.id !== "3"),
        ...manimTasks,
        { id: "3", name: "Generating Animations", status: "in-progress" },
      ]);

      const scriptsWithCode: ScriptItem[] = [];

      for (const scriptItem of scripts) {
        setTasks((prevTasks) =>
          prevTasks.map((task) =>
            task.id === `manim-${scriptItem.title}`
              ? { ...task, status: "in-progress" }
              : task
          )
        );

        const code = await generateManimCodeWithFetch(
          scriptItem.title,
          scriptItem.description,
          currentPrompt
        );

        if (code) {
          console.log(`✅ Adding script "${scriptItem.title}" to scriptsWithCode`);
          scriptsWithCode.push({ ...scriptItem, code });
        } else {
          console.warn(`⚠️ No code generated for script "${scriptItem.title}", skipping`);
        }
      }

      setTasks((prevTasks) =>
        prevTasks.map((task) =>
          task.id === "3" ? { ...task, status: "completed" } : task
        )
      );

      console.log("🎬 Manim code generation completed!");
      console.log(`📊 Scripts with code: ${scriptsWithCode.length}`);
      console.log(`🆔 Quiz ID: ${quizId}`);
      console.log("📝 Scripts with code:", scriptsWithCode.map(s => ({ title: s.title, hasCode: !!s.code })));

      // Trigger video rendering after animation generation is complete
      if (scriptsWithCode.length > 0 && quizId) {
        console.log("✅ All conditions met, starting video rendering...");

        // Show confirmation dialog first
        setShowConfirmationDialog(true);

        // Start video rendering
        try {
          await renderVideoWithBackend(scriptsWithCode, quizId);
        } catch (error) {
          console.error("❌ Video rendering failed:", error);
          setTasks((prevTasks) =>
            prevTasks.map((task) =>
              task.id === "3"
                ? { ...task, status: "failed", name: "Animation Generation (Render Failed)" }
                : task
            )
          );
        }
      } else {
        console.warn("❌ Cannot start video rendering:");
        console.warn(`  - Scripts with code: ${scriptsWithCode.length}`);
        console.warn(`  - Quiz ID: ${quizId}`);
        console.warn("  - This means either no manim codes were generated or quiz creation failed");
      }
    },
    [generateManimCodeWithFetch, renderVideoWithBackend]
  );

  const handleGenerateClick = useCallback(
    async (prompt: string) => {
      // Check if user already has an active generation
      try {
        const response = await fetch("/api/check-generation-status");
        if (response.ok) {
          const { hasActiveGeneration, activeEntry } = await response.json();

          if (hasActiveGeneration) {
            alert(`You already have a video generation in progress for: "${activeEntry.prompt}". Please wait for it to complete before starting a new one.`);
            return;
          }
        }
      } catch (error) {
        console.error("Failed to check generation status:", error);
        // Continue with generation if check fails
      }

      setIsGenerating(true);
      setShowGenerationUI(true);
      setCurrentScripts([]);
      setQuizId(null);

      if (typeof window !== "undefined") {
        localStorage.setItem("currentPrompt", prompt);
        setMainTheme(prompt);
      }

      const initialTasks: Task[] = [
        { id: "1", name: "Analyzing Input", status: "in-progress" },
        { id: "2", name: "Generating Script", status: "pending" },
        { id: "3", name: "Generating Animations", status: "pending" },
      ];
      setTasks(initialTasks);

      try {
        setTasks((prevTasks) =>
          prevTasks.map((task) =>
            task.id === "2" ? { ...task, status: "in-progress" } : task
          )
        );
        await generateScriptWithFetch(prompt);
      } catch (error) {
        if (error instanceof Error && error.name === "AbortError") {
          console.log("Generation was aborted");
          return;
        }

        setTasks((prevTasks) =>
          prevTasks.map((task) =>
            task.status === "in-progress"
              ? {
                  ...task,
                  status: "failed",
                  name: `${task.name} (Error: ${
                    error instanceof Error ? error.message : String(error)
                  })`,
                }
              : task
          )
        );
      }
    },
    [generateScriptWithFetch]
  );

  const handleDialogClose = useCallback(() => {
    setShowConfirmationDialog(false);
  }, []);

  const handleResetToInput = useCallback(() => {
    setShowConfirmationDialog(false);
    setTimeout(() => {
      setIsGenerating(false);
      setShowGenerationUI(false);
      setCurrentScripts([]);
      setTasks([]);
      setVideoJobId(null);
      setQuizId(null);
    }, 300);
  }, []);

  return (
    <div className="h-full flex flex-col">
      <InputSection
        key={"input-section"}
        isGenerating={isGenerating}
        onGenerate={handleGenerateClick}
      />
      {showGenerationUI && (
        <div className="flex-1 w-full h-0 overflow-hidden">
          <div className="h-full w-full grid grid-cols-1 lg:grid-cols-7 gap-0">
            <TaskProgressSidebar isGenerating={isGenerating} tasks={tasks} />

            <ContentDisplayPanel
              isGenerating={isGenerating}
              currentScripts={currentScripts}
            />
          </div>
        </div>
      )}

      <VideoConfirmationDialog
        key={"video-confirmation-dialog"}
        isOpen={showConfirmationDialog}
        onClose={handleDialogClose}
        onContinueWorking={handleResetToInput}
        videoJobId={videoJobId}
        queuePosition={queuePosition}
      />
    </div>
  );
}
