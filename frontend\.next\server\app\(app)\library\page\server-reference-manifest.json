{"node": {"00aa565eb2d0e4f80facd334f98ad09567eff45fb7": {"workers": {"app/(app)/library/page": {"moduleId": "[project]/.next-internal/server/app/(app)/library/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/lib/authenticatedUser.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/actions/user.actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/actions/auth.actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/src/actions/quiz.actions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(app)/library/page": "rsc"}}, "4094e98bfb36c131131e02aa1472a5a3bb4ba9450a": {"workers": {"app/(app)/library/page": {"moduleId": "[project]/.next-internal/server/app/(app)/library/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/lib/authenticatedUser.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/actions/user.actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/actions/auth.actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/src/actions/quiz.actions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(app)/library/page": "rsc"}}, "40dbc3ead418eae12aee42777527fdfbc82310cbbf": {"workers": {"app/(app)/library/page": {"moduleId": "[project]/.next-internal/server/app/(app)/library/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/lib/authenticatedUser.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/actions/user.actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/actions/auth.actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/src/actions/quiz.actions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(app)/library/page": "rsc"}}, "7fcbbbed3abb26571f88b42ddd8f89a5fdca9cd647": {"workers": {"app/(app)/library/page": {"moduleId": "[project]/.next-internal/server/app/(app)/library/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/lib/authenticatedUser.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/actions/user.actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/actions/auth.actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/src/actions/quiz.actions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(app)/library/page": "action-browser"}}, "40333d092027a9dc35ba3db39c375ec4e6f2805d34": {"workers": {"app/(app)/library/page": {"moduleId": "[project]/.next-internal/server/app/(app)/library/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/lib/authenticatedUser.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/actions/user.actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/actions/auth.actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/src/actions/quiz.actions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(app)/library/page": "action-browser"}}}, "edge": {}}