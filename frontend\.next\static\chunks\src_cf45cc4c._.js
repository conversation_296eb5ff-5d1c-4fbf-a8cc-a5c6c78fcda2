(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/ui/sonner.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Toaster": (()=>Toaster)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-themes/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
const Toaster = ({ ...props })=>{
    _s();
    const { theme = "system" } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTheme"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Toaster"], {
        theme: theme,
        className: "toaster group",
        style: {
            "--normal-bg": "var(--popover)",
            "--normal-text": "var(--popover-foreground)",
            "--normal-border": "var(--border)"
        },
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/sonner.tsx",
        lineNumber: 10,
        columnNumber: 5
    }, this);
};
_s(Toaster, "EriOrahfenYKDCErPq+L6926Dw4=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTheme"]
    ];
});
_c = Toaster;
;
var _c;
__turbopack_context__.k.register(_c, "Toaster");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/contexts/GenerationContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "GenerationProvider": (()=>GenerationProvider),
    "dispatchVideoCompletion": (()=>dispatchVideoCompletion),
    "useGeneration": (()=>useGeneration)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
"use client";
;
;
const GenerationContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const initialState = {
    isGenerating: false,
    currentEntryId: null,
    videoJobId: null,
    queuePosition: null,
    isCompleted: false,
    completedVideoUrl: null
};
function GenerationProvider({ children }) {
    _s();
    const [state, setState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(initialState);
    // Listen for completion notifications
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "GenerationProvider.useEffect": ()=>{
            const handleCompletion = {
                "GenerationProvider.useEffect.handleCompletion": (event)=>{
                    const { entryId, videoUrl } = event.detail;
                    if (state.currentEntryId === entryId) {
                        setState({
                            "GenerationProvider.useEffect.handleCompletion": (prev)=>({
                                    ...prev,
                                    isGenerating: false,
                                    isCompleted: true,
                                    completedVideoUrl: videoUrl
                                })
                        }["GenerationProvider.useEffect.handleCompletion"]);
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Video generation completed!", {
                            description: "Your video is ready to view in the library.",
                            action: {
                                label: "View Library",
                                onClick: {
                                    "GenerationProvider.useEffect.handleCompletion": ()=>window.location.href = "/library"
                                }["GenerationProvider.useEffect.handleCompletion"]
                            }
                        });
                    }
                }
            }["GenerationProvider.useEffect.handleCompletion"];
            window.addEventListener('videoCompleted', handleCompletion);
            return ({
                "GenerationProvider.useEffect": ()=>{
                    window.removeEventListener('videoCompleted', handleCompletion);
                }
            })["GenerationProvider.useEffect"];
        }
    }["GenerationProvider.useEffect"], [
        state.currentEntryId
    ]);
    const startGeneration = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "GenerationProvider.useCallback[startGeneration]": (entryId, jobId, queuePosition)=>{
            setState({
                isGenerating: true,
                currentEntryId: entryId,
                videoJobId: jobId,
                queuePosition: queuePosition || null,
                isCompleted: false,
                completedVideoUrl: null
            });
        }
    }["GenerationProvider.useCallback[startGeneration]"], []);
    const stopGeneration = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "GenerationProvider.useCallback[stopGeneration]": ()=>{
            setState({
                "GenerationProvider.useCallback[stopGeneration]": (prev)=>({
                        ...prev,
                        isGenerating: false
                    })
            }["GenerationProvider.useCallback[stopGeneration]"]);
        }
    }["GenerationProvider.useCallback[stopGeneration]"], []);
    const updateQueuePosition = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "GenerationProvider.useCallback[updateQueuePosition]": (position)=>{
            setState({
                "GenerationProvider.useCallback[updateQueuePosition]": (prev)=>({
                        ...prev,
                        queuePosition: position
                    })
            }["GenerationProvider.useCallback[updateQueuePosition]"]);
        }
    }["GenerationProvider.useCallback[updateQueuePosition]"], []);
    const markCompleted = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "GenerationProvider.useCallback[markCompleted]": (videoUrl)=>{
            setState({
                "GenerationProvider.useCallback[markCompleted]": (prev)=>({
                        ...prev,
                        isGenerating: false,
                        isCompleted: true,
                        completedVideoUrl: videoUrl
                    })
            }["GenerationProvider.useCallback[markCompleted]"]);
        }
    }["GenerationProvider.useCallback[markCompleted]"], []);
    const resetState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "GenerationProvider.useCallback[resetState]": ()=>{
            setState(initialState);
        }
    }["GenerationProvider.useCallback[resetState]"], []);
    const value = {
        state,
        startGeneration,
        stopGeneration,
        updateQueuePosition,
        markCompleted,
        resetState
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(GenerationContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/GenerationContext.tsx",
        lineNumber: 116,
        columnNumber: 5
    }, this);
}
_s(GenerationProvider, "J1gDxF8gFvajlMDxFyTyDL5azv0=");
_c = GenerationProvider;
function useGeneration() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(GenerationContext);
    if (context === undefined) {
        throw new Error('useGeneration must be used within a GenerationProvider');
    }
    return context;
}
_s1(useGeneration, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
function dispatchVideoCompletion(entryId, videoUrl) {
    const event = new CustomEvent('videoCompleted', {
        detail: {
            entryId,
            videoUrl
        }
    });
    window.dispatchEvent(event);
}
var _c;
__turbopack_context__.k.register(_c, "GenerationProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_cf45cc4c._.js.map