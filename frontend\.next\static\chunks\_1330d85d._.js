(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/shared/LetterGlitch.tsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_components_shared_LetterGlitch_tsx_d8000249._.js",
  "static/chunks/src_components_shared_LetterGlitch_tsx_8affea0f._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/shared/LetterGlitch.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}}),
"[project]/node_modules/react-syntax-highlighter/dist/esm/index.js [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_d4af1fa6._.js",
  "static/chunks/node_modules_react-syntax-highlighter_dist_esm_55ceda8e._.js",
  "static/chunks/node_modules_highlight_js_lib_languages_mathematica_b6fce4e3.js",
  "static/chunks/node_modules_highlight_js_lib_languages_f5da2e43._.js",
  "static/chunks/node_modules_highlight_js_lib_core_af9e08a7.js",
  "static/chunks/node_modules_refractor_f85e00c5._.js",
  "static/chunks/node_modules_eb30a3e2._.js",
  "static/chunks/node_modules_react-syntax-highlighter_dist_esm_index_8a01d1ea.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/react-syntax-highlighter/dist/esm/index.js [app-client] (ecmascript, next/dynamic entry)");
    });
});
}}),
}]);