(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/mvpblocks/ContentDisplayPanel.tsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/_1330d85d._.js",
  "static/chunks/[root-of-the-server]__29ec26bc._.js",
  {
    "path": "static/chunks/[next]_internal_font_google_geist_mono_7e1f0032_module_b1e4eef1.css",
    "included": [
      "[next]/internal/font/google/geist_mono_7e1f0032.module.css [app-client] (css)"
    ]
  },
  "static/chunks/src_components_mvpblocks_ContentDisplayPanel_tsx_f077eb8e._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/mvpblocks/ContentDisplayPanel.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}}),
"[project]/src/components/mvpblocks/TaskProgressSidebar.tsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/_ec983ae7._.js",
  "static/chunks/src_components_mvpblocks_TaskProgressSidebar_tsx_f077eb8e._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/mvpblocks/TaskProgressSidebar.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}}),
}]);