{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/actions/auth.actions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { signIn, signOut } from \"@/lib/auth\";\r\nimport { revalidatePath } from \"next/cache\";\r\n\r\nexport const login = async (provider: string) => {\r\n  await signIn(provider, { redirectTo: \"/dashboard\" });\r\n  revalidatePath(\"/\");\r\n};\r\n\r\nexport const logout = async () => {\r\n  await signOut({ redirectTo: \"/\" });\r\n  revalidatePath(\"/\");\r\n};\r\n\r\nexport const resendLogin = async (email: string) => {\r\n  await signIn(\"resend\", {\r\n    email,\r\n    redirectTo: \"/dashboard\",\r\n  });\r\n  revalidatePath(\"/dashboard\");\r\n};"], "names": [], "mappings": ";;;;;;;AAEA;AACA;;;;;;AAEO,MAAM,QAAQ,OAAO;IAC1B,MAAM,CAAA,GAAA,kHAAA,CAAA,SAAM,AAAD,EAAE,UAAU;QAAE,YAAY;IAAa;IAClD,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;AACjB;AAEO,MAAM,SAAS;IACpB,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAO,AAAD,EAAE;QAAE,YAAY;IAAI;IAChC,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;AACjB;AAEO,MAAM,cAAc,OAAO;IAChC,MAAM,CAAA,GAAA,kHAAA,CAAA,SAAM,AAAD,EAAE,UAAU;QACrB;QACA,YAAY;IACd;IACA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;AACjB;;;IAhBa;IAKA;IAKA;;AAVA,+OAAA;AAKA,+OAAA;AAKA,+OAAA", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/.next-internal/server/app/%28app%29/generate/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {getAuthenticatedUser as '00aa565eb2d0e4f80facd334f98ad09567eff45fb7'} from 'ACTIONS_MODULE0'\nexport {getUserFromEntriesId as '4094e98bfb36c131131e02aa1472a5a3bb4ba9450a'} from 'ACTIONS_MODULE1'\nexport {getUser as '40dbc3ead418eae12aee42777527fdfbc82310cbbf'} from 'ACTIONS_MODULE1'\nexport {logout as '7fcbbbed3abb26571f88b42ddd8f89a5fdca9cd647'} from 'ACTIONS_MODULE2'\n"], "names": [], "mappings": ";AAAA;AACA;AAEA", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/components/mvpblocks/bolt.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/mvpblocks/bolt.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/mvpblocks/bolt.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAqS,GAClU,mEACA", "debugId": null}}, {"offset": {"line": 142, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/components/mvpblocks/bolt.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/mvpblocks/bolt.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/mvpblocks/bolt.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiR,GAC9S,+CACA", "debugId": null}}, {"offset": {"line": 156, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/app/%28app%29/generate/page.tsx"], "sourcesContent": ["import BoltChat from \"@/components/mvpblocks/bolt\";\r\n\r\nexport default function GeneratePage() {\r\n  return (\r\n    <div className=\"h-full flex flex-col\">\r\n      <BoltChat />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,uIAAA,CAAA,UAAQ;;;;;;;;;;AAGf", "debugId": null}}]}