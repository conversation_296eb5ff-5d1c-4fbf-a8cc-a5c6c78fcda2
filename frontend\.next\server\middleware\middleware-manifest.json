{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_cb73fd16._.js", "server/edge/chunks/node_modules_@auth_core_b5890d95._.js", "server/edge/chunks/node_modules_jose_dist_webapi_49ff121e._.js", "server/edge/chunks/node_modules_74ea7e62._.js", "server/edge/chunks/[root-of-the-server]__b683c460._.js", "server/edge/chunks/edge-wrapper_551d8963.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "dVF7qFKM2cQ8/z46hw4X7IjD66wh96fiTI69TXHPOYA=", "__NEXT_PREVIEW_MODE_ID": "146c4f7c19e56a0b3e653beed08bdfc6", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "474b4b4380880f98ffa0232f0006f71bb1408e70b9e612198c2d0552dccee7f1", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "b6a5c548764455bf3b0314fdf45ae7e2ab7710a2fe4f0659a29875c088e634ee"}}}, "instrumentation": null, "functions": {}}