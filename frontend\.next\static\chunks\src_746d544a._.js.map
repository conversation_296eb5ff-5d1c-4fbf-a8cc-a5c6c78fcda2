{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/components/mvpblocks/InputSection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport {\r\n  <PERSON><PERSON>les,\r\n  Loader,\r\n  FileUp,\r\n  Terminal,\r\n  Monitor as MonitorIcon,\r\n  Figma,\r\n} from \"lucide-react\";\r\n\r\nconst EXAMPLE_ACTIONS = [\r\n  { icon: <Figma className=\"h-4 w-4\" />, text: \"Bayesian Theorem in ML\" },\r\n  { icon: <FileUp className=\"h-4 w-4\" />, text: \"Hidden Markov Models (HMM)\" },\r\n  {\r\n    icon: <MonitorIcon className=\"h-4 w-4\" />,\r\n    text: \"Gaussian Mixture Models\",\r\n  },\r\n  { icon: <Terminal className=\"h-4 w-4\" />, text: \"Linked Lists in DSA\" },\r\n  { icon: <FileUp className=\"h-4 w-4\" />, text: \"Binary Trees in DSA\" },\r\n  { icon: <Figma className=\"h-4 w-4\" />, text: \"Quadratic Equations in Maths\" },\r\n  {\r\n    icon: <FileUp className=\"h-4 w-4\" />,\r\n    text: \"Projectile Motion in Physics\",\r\n  },\r\n  {\r\n    icon: <MonitorIcon className=\"h-4 w-4\" />,\r\n    text: \"Dynamic Programming in DSA\",\r\n  },\r\n  {\r\n    icon: <Terminal className=\"h-4 w-4\" />,\r\n    text: \"Eigenvalues and Eigenvectors\",\r\n  },\r\n  { icon: <FileUp className=\"h-4 w-4\" />, text: \"Fourier Transform in Maths\" },\r\n  { icon: <Figma className=\"h-4 w-4\" />, text: \"Convex Optimization in Maths\" },\r\n  { icon: <MonitorIcon className=\"h-4 w-4\" />, text: \"Graph Theory in DSA\" },\r\n  { icon: <Terminal className=\"h-4 w-4\" />, text: \"Quantum Mechanics Basics\" },\r\n  { icon: <FileUp className=\"h-4 w-4\" />, text: \"Neural Networks in ML\" },\r\n];\r\n\r\ninterface InputSectionProps {\r\n  isGenerating: boolean;\r\n  onGenerate: (prompt: string) => void;\r\n}\r\n\r\nexport default function InputSection({\r\n  isGenerating,\r\n  onGenerate,\r\n}: InputSectionProps) {\r\n  const [inputValue, setInputValue] = useState(\"\");\r\n  const [isEnhancing, setIsEnhancing] = useState(false);\r\n\r\n  const enhancePrompt = async (prompt: string) => {\r\n    if (!prompt.trim()) {\r\n      setInputValue(\"\");\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setIsEnhancing(true);\r\n      localStorage.setItem(\"currentPrompt\", prompt.trim());\r\n\r\n      const response = await fetch(\"/api/enhance\", {\r\n        method: \"POST\",\r\n        headers: { \"Content-Type\": \"application/json\" },\r\n        body: JSON.stringify({ prompt: prompt.trim() }),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`HTTP error! status: ${response.status}`);\r\n      }\r\n\r\n      const enhancedPrompt = await response.text();\r\n      setInputValue(enhancedPrompt);\r\n    } catch (error) {\r\n      console.error(\"Error enhancing prompt:\", error);\r\n    } finally {\r\n      setIsEnhancing(false);\r\n    }\r\n  };\r\n\r\n  const handleGenerateClick = () => {\r\n    if (!inputValue.trim()) return;\r\n    // Store the full prompt value, not truncated\r\n    localStorage.setItem(\"currentPrompt\", inputValue.trim());\r\n    onGenerate(inputValue.trim());\r\n  };\r\n\r\n  return (\r\n    <AnimatePresence>\r\n      {!isGenerating && (\r\n        <motion.div\r\n          key=\"idle-ui\"\r\n          className=\"flex flex-grow flex-col h-full my-20 w-full items-center justify-center relative\"\r\n          initial={{ opacity: 0 }}\r\n          animate={{ opacity: 1 }}\r\n          exit={{ opacity: 0 }}\r\n          transition={{ duration: 0.4 }}\r\n        >\r\n          <div className=\"flex w-1/2 h-24 rounded-full bg-primary/20 blur-3xl absolute -top-10 left-1/2 -translate-x-1/2 text-foreground overflow-hidden\" />\r\n          <div className=\"mx-4 flex flex-col items-center\">\r\n            <div className=\"mb-12 text-center\">\r\n              <h1 className=\"mb-6 text-5xl md:text-6xl font-medium tracking-tight text-transparent bg-clip-text bg-gradient-to-br from-foreground to-muted/70 via-foreground/80\">\r\n                What do you want to learn?\r\n              </h1>\r\n              <p className=\"text-lg text-muted-foreground max-w-md mx-auto\">\r\n                Create animated explanations for any complex topic in minutes.\r\n                For both{\" \"}\r\n                <span className=\"font-medium text-foreground\">students</span>{\" \"}\r\n                and{\" \"}\r\n                <span className=\"font-medium text-foreground\">teachers</span> .\r\n              </p>\r\n            </div>\r\n\r\n            <div className=\"mx-auto mb-6 w-full max-w-xl\">\r\n              <div className=\"shadow-xl dark:shadow-primary/20 dark:shadow-2xl relative rounded-lg\">\r\n                <div className=\"flex flex-col rounded-lg border bg-gradient-to-b from-secondary/40 to-background p-3 pb-6 relative overflow-hidden\">\r\n                  <div className=\"absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent to-transparent via-primary pointer-events-none select-none\"></div>\r\n                  <div className=\"absolute bottom-0 left-0 w-full h-3 bg-gradient-to-r from-transparent to-transparent via-primary pointer-events-none select-none blur-2xl\"></div>\r\n                  <textarea\r\n                    placeholder=\"Explain bayes theorem in machine learning\"\r\n                    className=\"h-32 w-full outline-none resize-none text-sm\"\r\n                    value={inputValue}\r\n                    onChange={(e) => setInputValue(e.target.value)}\r\n                  />\r\n                  <div className=\"mt-auto flex gap-2 absolute bottom-2 right-2 \">\r\n                    <Button\r\n                      variant=\"ghost\"\r\n                      size=\"sm\"\r\n                      className={cn(\r\n                        \"backdrop-blur-lg shadow\",\r\n                        isEnhancing ? \"animate-pulse\" : null\r\n                      )}\r\n                      disabled={\r\n                        !inputValue.trim() ||\r\n                        isEnhancing ||\r\n                        inputValue.length < 6 ||\r\n                        inputValue.length > 300\r\n                      }\r\n                      onClick={() => {\r\n                        if (!inputValue.trim()) return;\r\n                        enhancePrompt(inputValue.trim());\r\n                      }}\r\n                    >\r\n                      {isEnhancing ? (\r\n                        <Loader className=\"animate-spin size-4\" />\r\n                      ) : (\r\n                        <Sparkles className=\"size-4\" />\r\n                      )}\r\n                      {isEnhancing ? \"Enhancing...\" : \"Enhance\"}\r\n                    </Button>\r\n                    <Button\r\n                      size=\"sm\"\r\n                      onClick={handleGenerateClick}\r\n                      disabled={!inputValue.trim() || isEnhancing}\r\n                    >\r\n                      Generate\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"mx-auto mt-16 flex w-full max-w-6xl flex-wrap justify-center gap-2\">\r\n              {EXAMPLE_ACTIONS.map((action, index) => (\r\n                <Button\r\n                  key={index}\r\n                  size=\"sm\"\r\n                  variant=\"outline\"\r\n                  className=\"rounded-full px-4 py-0.5 text-xs\"\r\n                  onClick={() => setInputValue(action.text)}\r\n                >\r\n                  {action.icon}\r\n                  <span>{action.text}</span>\r\n                </Button>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </motion.div>\r\n      )}\r\n    </AnimatePresence>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;;;;AAeA,MAAM,kBAAkB;IACtB;QAAE,oBAAM,6LAAC,uMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QAAc,MAAM;IAAyB;IACtE;QAAE,oBAAM,6LAAC,6MAAA,CAAA,SAAM;YAAC,WAAU;;;;;;QAAc,MAAM;IAA6B;IAC3E;QACE,oBAAM,6LAAC,2MAAA,CAAA,UAAW;YAAC,WAAU;;;;;;QAC7B,MAAM;IACR;IACA;QAAE,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QAAc,MAAM;IAAsB;IACtE;QAAE,oBAAM,6LAAC,6MAAA,CAAA,SAAM;YAAC,WAAU;;;;;;QAAc,MAAM;IAAsB;IACpE;QAAE,oBAAM,6LAAC,uMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QAAc,MAAM;IAA+B;IAC5E;QACE,oBAAM,6LAAC,6MAAA,CAAA,SAAM;YAAC,WAAU;;;;;;QACxB,MAAM;IACR;IACA;QACE,oBAAM,6LAAC,2MAAA,CAAA,UAAW;YAAC,WAAU;;;;;;QAC7B,MAAM;IACR;IACA;QACE,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QAC1B,MAAM;IACR;IACA;QAAE,oBAAM,6LAAC,6MAAA,CAAA,SAAM;YAAC,WAAU;;;;;;QAAc,MAAM;IAA6B;IAC3E;QAAE,oBAAM,6LAAC,uMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QAAc,MAAM;IAA+B;IAC5E;QAAE,oBAAM,6LAAC,2MAAA,CAAA,UAAW;YAAC,WAAU;;;;;;QAAc,MAAM;IAAsB;IACzE;QAAE,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QAAc,MAAM;IAA2B;IAC3E;QAAE,oBAAM,6LAAC,6MAAA,CAAA,SAAM;YAAC,WAAU;;;;;;QAAc,MAAM;IAAwB;CACvE;AAOc,SAAS,aAAa,EACnC,YAAY,EACZ,UAAU,EACQ;;IAClB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,gBAAgB,OAAO;QAC3B,IAAI,CAAC,OAAO,IAAI,IAAI;YAClB,cAAc;YACd;QACF;QAEA,IAAI;YACF,eAAe;YACf,aAAa,OAAO,CAAC,iBAAiB,OAAO,IAAI;YAEjD,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE,QAAQ,OAAO,IAAI;gBAAG;YAC/C;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,iBAAiB,MAAM,SAAS,IAAI;YAC1C,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,WAAW,IAAI,IAAI;QACxB,6CAA6C;QAC7C,aAAa,OAAO,CAAC,iBAAiB,WAAW,IAAI;QACrD,WAAW,WAAW,IAAI;IAC5B;IAEA,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACb,CAAC,8BACA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YAET,WAAU;YACV,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;YACnB,YAAY;gBAAE,UAAU;YAAI;;8BAE5B,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAqJ;;;;;;8CAGnK,6LAAC;oCAAE,WAAU;;wCAAiD;wCAEnD;sDACT,6LAAC;4CAAK,WAAU;sDAA8B;;;;;;wCAAgB;wCAAI;wCAC9D;sDACJ,6LAAC;4CAAK,WAAU;sDAA8B;;;;;;wCAAe;;;;;;;;;;;;;sCAIjE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CACC,aAAY;4CACZ,WAAU;4CACV,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;sDAE/C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2BACA,cAAc,kBAAkB;oDAElC,UACE,CAAC,WAAW,IAAI,MAChB,eACA,WAAW,MAAM,GAAG,KACpB,WAAW,MAAM,GAAG;oDAEtB,SAAS;wDACP,IAAI,CAAC,WAAW,IAAI,IAAI;wDACxB,cAAc,WAAW,IAAI;oDAC/B;;wDAEC,4BACC,6LAAC,yMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;iFAElB,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAErB,cAAc,iBAAiB;;;;;;;8DAElC,6LAAC,qIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAS;oDACT,UAAU,CAAC,WAAW,IAAI,MAAM;8DACjC;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQT,6LAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC,QAAQ,sBAC5B,6LAAC,qIAAA,CAAA,SAAM;oCAEL,MAAK;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,cAAc,OAAO,IAAI;;wCAEvC,OAAO,IAAI;sDACZ,6LAAC;sDAAM,OAAO,IAAI;;;;;;;mCAPb;;;;;;;;;;;;;;;;;WA1ET;;;;;;;;;;AA0Fd;GAzIwB;KAAA", "debugId": null}}, {"offset": {"line": 438, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Dialog({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\r\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\r\n}\r\n\r\nfunction DialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\r\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\r\n}\r\n\r\nfunction DialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\r\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\r\n}\r\n\r\nfunction DialogClose({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\r\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\r\n}\r\n\r\nfunction DialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\r\n  return (\r\n    <DialogPrimitive.Overlay\r\n      data-slot=\"dialog-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogContent({\r\n  className,\r\n  children,\r\n  showCloseButton = true,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\r\n  showCloseButton?: boolean\r\n}) {\r\n  return (\r\n    <DialogPortal data-slot=\"dialog-portal\">\r\n      <DialogOverlay />\r\n      <DialogPrimitive.Content\r\n        data-slot=\"dialog-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        {showCloseButton && (\r\n          <DialogPrimitive.Close\r\n            data-slot=\"dialog-close\"\r\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\r\n          >\r\n            <XIcon />\r\n            <span className=\"sr-only\">Close</span>\r\n          </DialogPrimitive.Close>\r\n        )}\r\n      </DialogPrimitive.Content>\r\n    </DialogPortal>\r\n  )\r\n}\r\n\r\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-header\"\r\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-footer\"\r\n      className={cn(\r\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\r\n  return (\r\n    <DialogPrimitive.Title\r\n      data-slot=\"dialog-title\"\r\n      className={cn(\"text-lg leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\r\n  return (\r\n    <DialogPrimitive.Description\r\n      data-slot=\"dialog-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Dialog,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogOverlay,\r\n  DialogPortal,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,6LAAC,qKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,6LAAC,mMAAA,CAAA,QAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;MAhCS;AAkCT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 636, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/components/mvpblocks/VideoConfirmationDialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { motion } from \"framer-motion\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from \"@/components/ui/dialog\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { CheckCircle2, Clock, Video, ArrowRight, ExternalLink } from \"lucide-react\";\r\nimport { useGeneration } from \"@/contexts/GenerationContext\";\r\n\r\ninterface VideoConfirmationDialogProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  onContinueWorking?: () => void;\r\n  videoJobId: string | null;\r\n  queuePosition?: number | null;\r\n}\r\n\r\nexport default function VideoConfirmationDialog({\r\n  isOpen,\r\n  onClose,\r\n  onContinueWorking,\r\n  videoJobId,\r\n  queuePosition,\r\n}: VideoConfirmationDialogProps) {\r\n  const { state } = useGeneration();\r\n\r\n  const handleGoToDashboard = () => {\r\n    onClose();\r\n    window.location.href = \"/dashboard\";\r\n  };\r\n\r\n  const handleGoToLibrary = () => {\r\n    onClose();\r\n    window.location.href = \"/library\";\r\n  };\r\n\r\n  const handleContinueWorking = () => {\r\n    // Use custom handler if provided, otherwise just close\r\n    if (onContinueWorking) {\r\n      onContinueWorking();\r\n    } else {\r\n      onClose();\r\n    }\r\n  };\r\n\r\n  // Check if video is completed\r\n  const isCompleted = state.isCompleted && state.completedVideoUrl;\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={onClose}>\r\n      <DialogContent>\r\n        <DialogHeader className=\"text-center space-y-4\">\r\n          <div className=\"mx-auto w-16 h-16 bg-gradient-to-br from-green-400/20 to-emerald-500/10 rounded-full flex items-center justify-center\">\r\n            <motion.div\r\n              initial={{ scale: 0 }}\r\n              animate={{ scale: 1 }}\r\n              transition={{ delay: 0.2, type: \"spring\", stiffness: 200 }}\r\n            >\r\n              <CheckCircle2 className=\"w-8 h-8 text-green-500\" />\r\n            </motion.div>\r\n          </div>\r\n\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 10 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ delay: 0.3 }}\r\n          >\r\n            <DialogTitle>\r\n              {isCompleted ? \"Video Generation Complete!\" : \"Video Generation Started!\"}\r\n            </DialogTitle>\r\n          </motion.div>\r\n        </DialogHeader>\r\n\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 10 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.4 }}\r\n          className=\"space-y-4\"\r\n        >\r\n          <DialogDescription className=\"text-muted-foreground leading-relaxed\">\r\n            Your animated educational video is now being generated. This process\r\n            typically takes 3-5 minutes.\r\n          </DialogDescription>\r\n\r\n          {videoJobId && (\r\n            <div className=\"bg-gradient-to-r from-primary/5 to-secondary/5 rounded-lg p-4 border border-primary/10\">\r\n              <div className=\"flex items-center space-x-3\">\r\n                <div className=\"w-2 h-2 rounded-full bg-gradient-to-r from-primary to-secondary animate-pulse\"></div>\r\n                <div className=\"flex-1\">\r\n                  <div className=\"text-sm font-medium text-foreground\">\r\n                    Job ID: {videoJobId}\r\n                  </div>\r\n                  {queuePosition && (\r\n                    <div className=\"text-xs text-muted-foreground\">\r\n                      Queue Position: #{queuePosition}\r\n                    </div>\r\n                  )}\r\n                  <div className=\"text-xs text-muted-foreground\">\r\n                    Track your video generation progress\r\n                  </div>\r\n                </div>\r\n                <Video className=\"w-5 h-5 text-primary\" />\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          <div className=\"bg-gradient-to-r from-blue-500/5 to-cyan-500/5 rounded-lg p-4 border border-blue-500/10\">\r\n            <div className=\"flex items-start space-x-3\">\r\n              <Clock className=\"w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0\" />\r\n              <div className=\"space-y-2\">\r\n                <p className=\"text-sm font-medium text-foreground\">\r\n                  What happens next?\r\n                </p>\r\n                <ul className=\"text-xs text-muted-foreground space-y-1\">\r\n                  <li>• Your video will be processed in the background</li>\r\n                  <li>• You'll receive a notification when it's ready</li>\r\n                  <li>• The video will be available in your dashboard</li>\r\n                  <li>• You can download or share it directly</li>\r\n                </ul>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </motion.div>\r\n\r\n        <DialogFooter className=\"flex-col sm:flex-row gap-2\">\r\n          {isCompleted ? (\r\n            <>\r\n              <Button\r\n                variant=\"outline\"\r\n                onClick={handleContinueWorking}\r\n                className=\"w-full sm:w-auto\"\r\n              >\r\n                Generate Another\r\n              </Button>\r\n              <Button onClick={handleGoToLibrary} className=\"bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600\">\r\n                <span>View in Library</span>\r\n                <ExternalLink className=\"w-4 h-4 ml-2\" />\r\n              </Button>\r\n            </>\r\n          ) : (\r\n            <>\r\n              <Button\r\n                variant=\"outline\"\r\n                onClick={handleContinueWorking}\r\n                className=\"w-full sm:w-auto\"\r\n              >\r\n                Continue Working\r\n              </Button>\r\n              <Button onClick={handleGoToDashboard}>\r\n                <span>Go to Dashboard</span>\r\n                <ArrowRight className=\"w-4 h-4 ml-2\" />\r\n              </Button>\r\n            </>\r\n          )}\r\n        </DialogFooter>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAbA;;;;;;AAuBe,SAAS,wBAAwB,EAC9C,MAAM,EACN,OAAO,EACP,iBAAiB,EACjB,UAAU,EACV,aAAa,EACgB;;IAC7B,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD;IAE9B,MAAM,sBAAsB;QAC1B;QACA,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,MAAM,oBAAoB;QACxB;QACA,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,MAAM,wBAAwB;QAC5B,uDAAuD;QACvD,IAAI,mBAAmB;YACrB;QACF,OAAO;YACL;QACF;IACF;IAEA,8BAA8B;IAC9B,MAAM,cAAc,MAAM,WAAW,IAAI,MAAM,iBAAiB;IAEhE,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;;8BACZ,6LAAC,qIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,OAAO;gCAAE;gCACpB,SAAS;oCAAE,OAAO;gCAAE;gCACpB,YAAY;oCAAE,OAAO;oCAAK,MAAM;oCAAU,WAAW;gCAAI;0CAEzD,cAAA,6LAAC,wNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;;;;;sCAI5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;4BAAI;sCAEzB,cAAA,6LAAC,qIAAA,CAAA,cAAW;0CACT,cAAc,+BAA+B;;;;;;;;;;;;;;;;;8BAKpD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,OAAO;oBAAI;oBACzB,WAAU;;sCAEV,6LAAC,qIAAA,CAAA,oBAAiB;4BAAC,WAAU;sCAAwC;;;;;;wBAKpE,4BACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;oDAAsC;oDAC1C;;;;;;;4CAEV,+BACC,6LAAC;gDAAI,WAAU;;oDAAgC;oDAC3B;;;;;;;0DAGtB,6LAAC;gDAAI,WAAU;0DAAgC;;;;;;;;;;;;kDAIjD,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKvB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAsC;;;;;;0DAGnD,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;kEAAG;;;;;;kEACJ,6LAAC;kEAAG;;;;;;kEACJ,6LAAC;kEAAG;;;;;;kEACJ,6LAAC;kEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOd,6LAAC,qIAAA,CAAA,eAAY;oBAAC,WAAU;8BACrB,4BACC;;0CACE,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS;gCACT,WAAU;0CACX;;;;;;0CAGD,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAS;gCAAmB,WAAU;;kDAC5C,6LAAC;kDAAK;;;;;;kDACN,6LAAC,yNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;;qDAI5B;;0CACE,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS;gCACT,WAAU;0CACX;;;;;;0CAGD,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAS;;kDACf,6LAAC;kDAAK;;;;;;kDACN,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtC;GA7IwB;;QAOJ,wIAAA,CAAA,gBAAa;;;KAPT", "debugId": null}}, {"offset": {"line": 1031, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/hooks/useAuthUser.ts"], "sourcesContent": ["// hooks/useAuthUser.ts\r\nimport { useEffect, useState } from \"react\";\r\n\r\nexport function useAuthUser() {\r\n  const [user, setUser] = useState<any>(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [unauthenticated, setUnauthenticated] = useState(false);\r\n\r\n  useEffect(() => {\r\n    async function fetchUser() {\r\n      try {\r\n        const res = await fetch(\"/api/user\");\r\n        if (res.status === 401) {\r\n          setUnauthenticated(true);\r\n          setUser(null);\r\n        } else {\r\n          const data = await res.json();\r\n          setUser(data.user);\r\n        }\r\n      } catch (err) {\r\n        console.error(\"Failed to fetch user\", err);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    }\r\n\r\n    fetchUser();\r\n  }, []);\r\n\r\n  return { user, loading, unauthenticated };\r\n}\r\n"], "names": [], "mappings": "AAAA,uBAAuB;;;;AACvB;;;AAEO,SAAS;;IACd,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IACtC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,eAAe;gBACb,IAAI;oBACF,MAAM,MAAM,MAAM,MAAM;oBACxB,IAAI,IAAI,MAAM,KAAK,KAAK;wBACtB,mBAAmB;wBACnB,QAAQ;oBACV,OAAO;wBACL,MAAM,OAAO,MAAM,IAAI,IAAI;wBAC3B,QAAQ,KAAK,IAAI;oBACnB;gBACF,EAAE,OAAO,KAAK;oBACZ,QAAQ,KAAK,CAAC,wBAAwB;gBACxC,SAAU;oBACR,WAAW;gBACb;YACF;YAEA;QACF;gCAAG,EAAE;IAEL,OAAO;QAAE;QAAM;QAAS;IAAgB;AAC1C;GA3BgB", "debugId": null}}, {"offset": {"line": 1080, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/components/mvpblocks/bolt.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, useRef, useCallback } from \"react\";\r\nimport InputSection from \"./InputSection\";\r\nimport VideoConfirmationDialog from \"./VideoConfirmationDialog\";\r\nimport { type Entries } from \"@prisma/client\";\r\nimport { useAuthUser } from \"@/hooks/useAuthUser\";\r\nimport dynamic from \"next/dynamic\";\r\n\r\nconst ContentDisplayPanel = dynamic(() => import(\"./ContentDisplayPanel\"), {\r\n  ssr: false,\r\n});\r\n\r\nconst TaskProgressSidebar = dynamic(() => import(\"./TaskProgressSidebar\"), {\r\n  ssr: false,\r\n});\r\n\r\ninterface ScriptItem {\r\n  title: string;\r\n  description: string;\r\n  code?: string;\r\n}\r\n\r\ninterface Task {\r\n  id: string;\r\n  name: string;\r\n  status: \"pending\" | \"in-progress\" | \"completed\" | \"failed\";\r\n}\r\n\r\nconst BACKEND_URL =\r\n  process.env.NEXT_PUBLIC_BACKEND_URL || \"http://localhost:8000\";\r\n\r\nexport default function Bolt() {\r\n  const { user } = useAuthUser();\r\n  const abortControllerRef = useRef<AbortController | null>(null);\r\n  const [isGenerating, setIsGenerating] = useState(false);\r\n  const [showGenerationUI, setShowGenerationUI] = useState(false);\r\n  const [tasks, setTasks] = useState<Task[]>([]);\r\n  const [currentScripts, setCurrentScripts] = useState<ScriptItem[]>([]);\r\n  const [showConfirmationDialog, setShowConfirmationDialog] = useState(false);\r\n  const [videoJobId, setVideoJobId] = useState<string | null>(null);\r\n  const [queuePosition, setQueuePosition] = useState<number | null>(null);\r\n  const [, setQuizId] = useState<string | null>(null);\r\n  const [mainTheme, setMainTheme] = useState<string>(\"\");\r\n\r\n  useEffect(() => {\r\n    if (typeof window !== \"undefined\") {\r\n      const savedPrompt = localStorage.getItem(\"currentPrompt\") || \"\";\r\n      setMainTheme(savedPrompt);\r\n    }\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    return () => {\r\n      if (abortControllerRef.current) {\r\n        abortControllerRef.current.abort();\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  const generateScriptWithFetch = useCallback(async (prompt: string) => {\r\n    try {\r\n      setIsGenerating(true);\r\n\r\n      abortControllerRef.current = new AbortController();\r\n\r\n      const response = await fetch(\"/api/generate-script\", {\r\n        method: \"POST\",\r\n        headers: { \"Content-Type\": \"application/json\" },\r\n        body: JSON.stringify({ prompt }),\r\n        signal: abortControllerRef.current.signal,\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`HTTP error! status: ${response.status}`);\r\n      }\r\n\r\n      const generatedScripts: ScriptItem[] = await response.json();\r\n      setCurrentScripts(generatedScripts);\r\n\r\n      setTasks((prevTasks) =>\r\n        prevTasks.map((task) =>\r\n          task.id === \"1\"\r\n            ? { ...task, status: \"completed\" }\r\n            : task.id === \"2\"\r\n            ? { ...task, status: \"completed\" }\r\n            : task\r\n        )\r\n      );\r\n\r\n      // Wait for quiz generation and get the quizId\r\n      const generatedQuizId = await generateQuizWithFetch(\r\n        generatedScripts.map((script) => script.title),\r\n        generatedScripts.map((script) => script.description)\r\n      );\r\n\r\n      // Pass the quizId to generateManimCodes\r\n      await generateManimCodes(generatedScripts, generatedQuizId, prompt);\r\n    } catch (error) {\r\n      if (error instanceof Error && error.name === \"AbortError\") {\r\n        console.log(\"Request was aborted\");\r\n        return;\r\n      }\r\n\r\n      setTasks((prevTasks) =>\r\n        prevTasks.map((task) =>\r\n          task.status === \"in-progress\"\r\n            ? {\r\n                ...task,\r\n                status: \"failed\",\r\n                name: `${task.name} (Error: ${\r\n                  error instanceof Error ? error.message : String(error)\r\n                })`,\r\n              }\r\n            : task\r\n        )\r\n      );\r\n    }\r\n  }, []);\r\n\r\n  const generateQuizWithFetch = async (\r\n    title: string[],\r\n    description: string[]\r\n  ): Promise<string | null> => {\r\n    try {\r\n      const combinedTitle = title.map((t) => t.trim()).join(\",\");\r\n      const combinedDescription = description.map((d) => d.trim()).join(\"\\n\");\r\n      const userId = user.id;\r\n\r\n      const response = await fetch(\"/api/ai-quiz\", {\r\n        method: \"POST\",\r\n        headers: { \"Content-Type\": \"application/json\" },\r\n        body: JSON.stringify({\r\n          title: combinedTitle,\r\n          content: combinedDescription,\r\n          userId: userId,\r\n        }),\r\n        signal: abortControllerRef.current?.signal,\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`HTTP error! status: ${response.status}`);\r\n      }\r\n\r\n      const quizData = (await response.json()) as Entries;\r\n      setQuizId(quizData.id);\r\n      return quizData.id; // Return the quizId\r\n    } catch (error) {\r\n      if (error instanceof Error && error.name === \"AbortError\") {\r\n        return null;\r\n      }\r\n\r\n      console.error(\"Error generating quiz:\", error);\r\n      return null;\r\n    }\r\n  };\r\n\r\n  const generateManimCodeWithFetch = useCallback(\r\n    async (\r\n      title: string,\r\n      description: string,\r\n      currentPrompt: string\r\n    ): Promise<string | null> => {\r\n      try {\r\n        const response = await fetch(\"/api/manim\", {\r\n          method: \"POST\",\r\n          headers: { \"Content-Type\": \"application/json\" },\r\n          body: JSON.stringify({\r\n            schema: { title, description },\r\n            mainTheme: currentPrompt,\r\n          }),\r\n          signal: abortControllerRef.current?.signal,\r\n        });\r\n\r\n        if (!response.ok) {\r\n          throw new Error(`HTTP error! status: ${response.status}`);\r\n        }\r\n\r\n        const data = await response.json();\r\n        const code = data.code;\r\n\r\n        setCurrentScripts((prev) =>\r\n          prev.map((script) =>\r\n            script.title === title ? { ...script, code: code } : script\r\n          )\r\n        );\r\n\r\n        setTasks((prevTasks) =>\r\n          prevTasks.map((task) =>\r\n            task.id === `manim-${title}`\r\n              ? { ...task, status: \"completed\" }\r\n              : task\r\n          )\r\n        );\r\n\r\n        return code;\r\n      } catch (error) {\r\n        if (error instanceof Error && error.name === \"AbortError\") {\r\n          return null;\r\n        }\r\n\r\n        setTasks((prevTasks) =>\r\n          prevTasks.map((task) =>\r\n            task.id === `manim-${title}`\r\n              ? {\r\n                  ...task,\r\n                  status: \"failed\",\r\n                  name: `${task.name} (Error: ${\r\n                    error instanceof Error ? error.message : String(error)\r\n                  })`,\r\n                }\r\n              : task\r\n          )\r\n        );\r\n        return null;\r\n      }\r\n    },\r\n    [] // Remove mainTheme dependency since we're passing it as parameter\r\n  );\r\n\r\n  const renderVideoWithBackend = useCallback(\r\n    async (scripts: ScriptItem[], quizId: string) => {\r\n      try {\r\n        console.log(\"Rendering video with backend\");\r\n\r\n        const manimCodes = scripts\r\n          .filter((script) => script.code)\r\n          .map((script) => script.code!);\r\n\r\n        if (manimCodes.length === 0) {\r\n          throw new Error(\"No valid scripts to render\");\r\n        }\r\n\r\n        if (!quizId) {\r\n          throw new Error(\"Quiz ID not available\");\r\n        }\r\n\r\n        // Prepare the batch render payload with the new format\r\n        const scriptsData = scripts\r\n          .filter((script) => script.code)\r\n          .map((script) => ({\r\n            manim_code: script.code!,\r\n            description: script.description,\r\n          }));\r\n\r\n        const renderPayload = {\r\n          topicName: mainTheme || \"Generated Topic\",\r\n          entryId: quizId,\r\n          scripts: scriptsData,\r\n          priority: 0,\r\n        };\r\n\r\n        // Mark entry as generating in database\r\n        try {\r\n          await fetch(\"/api/start-generation\", {\r\n            method: \"POST\",\r\n            headers: { \"Content-Type\": \"application/json\" },\r\n            body: JSON.stringify({ entryId: quizId }),\r\n          });\r\n          console.log(\"✅ Entry marked as generating in database\");\r\n        } catch (dbError) {\r\n          console.error(\"⚠️ Failed to mark entry as generating:\", dbError);\r\n          // Continue with video generation even if DB update fails\r\n        }\r\n\r\n        console.log(\"Sending batch render request:\", renderPayload);\r\n\r\n        const response = await fetch(`${BACKEND_URL}/batch_render`, {\r\n          method: \"POST\",\r\n          headers: { \"Content-Type\": \"application/json\" },\r\n          body: JSON.stringify(renderPayload),\r\n          signal: abortControllerRef.current?.signal,\r\n        });\r\n\r\n        if (!response.ok) {\r\n          const errorText = await response.text();\r\n          throw new Error(`Backend error: ${response.status} - ${errorText}`);\r\n        }\r\n\r\n        const result = await response.json();\r\n        console.log(\"Batch render response:\", result);\r\n\r\n        setVideoJobId(result.job_id);\r\n        setQueuePosition(result.queue_position);\r\n\r\n        setTasks((prevTasks) => [\r\n          ...prevTasks,\r\n          {\r\n            id: \"4\",\r\n            name: \"Rendering Video\",\r\n            status: \"completed\",\r\n          },\r\n        ]);\r\n\r\n        // Show confirmation dialog after a short delay\r\n        setTimeout(() => {\r\n          setIsGenerating(false);\r\n          setShowConfirmationDialog(true);\r\n        }, 500);\r\n      } catch (error) {\r\n        if (error instanceof Error && error.name === \"AbortError\") {\r\n          return;\r\n        }\r\n\r\n        console.error(\"Error rendering video:\", error);\r\n        setTasks((prevTasks) => [\r\n          ...prevTasks,\r\n          {\r\n            id: \"4\",\r\n            name: `Video Rendering Failed: ${\r\n              error instanceof Error ? error.message : String(error)\r\n            }`,\r\n            status: \"failed\",\r\n          },\r\n        ]);\r\n      }\r\n    },\r\n    []\r\n  );\r\n\r\n  const generateManimCodes = useCallback(\r\n    async (\r\n      scripts: ScriptItem[],\r\n      quizId: string | null,\r\n      currentPrompt: string\r\n    ) => {\r\n      const manimTasks: Task[] = scripts.map((script) => ({\r\n        id: `manim-${script.title}`,\r\n        name: `Generating: ${script.title}`,\r\n        status: \"pending\",\r\n      }));\r\n\r\n      setTasks((prevTasks) => [\r\n        ...prevTasks.filter((t) => t.id !== \"3\"),\r\n        ...manimTasks,\r\n        { id: \"3\", name: \"Generating Animations\", status: \"in-progress\" },\r\n      ]);\r\n\r\n      const scriptsWithCode: ScriptItem[] = [];\r\n\r\n      for (const scriptItem of scripts) {\r\n        setTasks((prevTasks) =>\r\n          prevTasks.map((task) =>\r\n            task.id === `manim-${scriptItem.title}`\r\n              ? { ...task, status: \"in-progress\" }\r\n              : task\r\n          )\r\n        );\r\n\r\n        const code = await generateManimCodeWithFetch(\r\n          scriptItem.title,\r\n          scriptItem.description,\r\n          currentPrompt\r\n        );\r\n\r\n        if (code) {\r\n          scriptsWithCode.push({ ...scriptItem, code });\r\n        }\r\n      }\r\n\r\n      setTasks((prevTasks) =>\r\n        prevTasks.map((task) =>\r\n          task.id === \"3\" ? { ...task, status: \"completed\" } : task\r\n        )\r\n      );\r\n\r\n      // Show video confirmation after animation generation is complete\r\n      if (scriptsWithCode.length > 0 && quizId) {\r\n        setTimeout(() => {\r\n          renderVideoWithBackend(scriptsWithCode, quizId);\r\n        }, 1000);\r\n      } else {\r\n        console.warn(\"No scripts with code generated or quiz ID missing\");\r\n      }\r\n    },\r\n    [generateManimCodeWithFetch, renderVideoWithBackend]\r\n  );\r\n\r\n  const handleGenerateClick = useCallback(\r\n    async (prompt: string) => {\r\n      // Check if user already has an active generation\r\n      try {\r\n        const response = await fetch(\"/api/check-generation-status\");\r\n        if (response.ok) {\r\n          const { hasActiveGeneration, activeEntry } = await response.json();\r\n\r\n          if (hasActiveGeneration) {\r\n            alert(`You already have a video generation in progress for: \"${activeEntry.prompt}\". Please wait for it to complete before starting a new one.`);\r\n            return;\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Failed to check generation status:\", error);\r\n        // Continue with generation if check fails\r\n      }\r\n\r\n      setIsGenerating(true);\r\n      setShowGenerationUI(true);\r\n      setCurrentScripts([]);\r\n      setQuizId(null);\r\n\r\n      if (typeof window !== \"undefined\") {\r\n        localStorage.setItem(\"currentPrompt\", prompt);\r\n        setMainTheme(prompt);\r\n      }\r\n\r\n      const initialTasks: Task[] = [\r\n        { id: \"1\", name: \"Analyzing Input\", status: \"in-progress\" },\r\n        { id: \"2\", name: \"Generating Script\", status: \"pending\" },\r\n        { id: \"3\", name: \"Generating Animations\", status: \"pending\" },\r\n      ];\r\n      setTasks(initialTasks);\r\n\r\n      try {\r\n        setTasks((prevTasks) =>\r\n          prevTasks.map((task) =>\r\n            task.id === \"2\" ? { ...task, status: \"in-progress\" } : task\r\n          )\r\n        );\r\n        await generateScriptWithFetch(prompt);\r\n      } catch (error) {\r\n        if (error instanceof Error && error.name === \"AbortError\") {\r\n          console.log(\"Generation was aborted\");\r\n          return;\r\n        }\r\n\r\n        setTasks((prevTasks) =>\r\n          prevTasks.map((task) =>\r\n            task.status === \"in-progress\"\r\n              ? {\r\n                  ...task,\r\n                  status: \"failed\",\r\n                  name: `${task.name} (Error: ${\r\n                    error instanceof Error ? error.message : String(error)\r\n                  })`,\r\n                }\r\n              : task\r\n          )\r\n        );\r\n      }\r\n    },\r\n    [generateScriptWithFetch]\r\n  );\r\n\r\n  const handleDialogClose = useCallback(() => {\r\n    setShowConfirmationDialog(false);\r\n  }, []);\r\n\r\n  const handleResetToInput = useCallback(() => {\r\n    setShowConfirmationDialog(false);\r\n    setTimeout(() => {\r\n      setIsGenerating(false);\r\n      setShowGenerationUI(false);\r\n      setCurrentScripts([]);\r\n      setTasks([]);\r\n      setVideoJobId(null);\r\n      setQuizId(null);\r\n    }, 300);\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"h-full flex flex-col\">\r\n      <InputSection\r\n        key={\"input-section\"}\r\n        isGenerating={isGenerating}\r\n        onGenerate={handleGenerateClick}\r\n      />\r\n      {showGenerationUI && (\r\n        <div className=\"flex-1 w-full h-0 overflow-hidden\">\r\n          <div className=\"h-full w-full grid grid-cols-1 lg:grid-cols-7 gap-0\">\r\n            <TaskProgressSidebar isGenerating={isGenerating} tasks={tasks} />\r\n\r\n            <ContentDisplayPanel\r\n              isGenerating={isGenerating}\r\n              currentScripts={currentScripts}\r\n            />\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      <VideoConfirmationDialog\r\n        key={\"video-confirmation-dialog\"}\r\n        isOpen={showConfirmationDialog}\r\n        onClose={handleDialogClose}\r\n        onContinueWorking={handleResetToInput}\r\n        videoJobId={videoJobId}\r\n        queuePosition={queuePosition}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;AA8BE;;AA5BF;AACA;AACA;AAEA;AACA;;;;;AAPA;;;;;;AASA,MAAM,sBAAsB,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;;;;;;IAClC,KAAK;;KADD;AAIN,MAAM,sBAAsB,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;;;;;;IAClC,KAAK;;MADD;AAgBN,MAAM,cACJ,8FAAuC;AAE1B,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAA0B;IAC1D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACrE,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,GAAG,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAEnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,wCAAmC;gBACjC,MAAM,cAAc,aAAa,OAAO,CAAC,oBAAoB;gBAC7D,aAAa;YACf;QACF;yBAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR;kCAAO;oBACL,IAAI,mBAAmB,OAAO,EAAE;wBAC9B,mBAAmB,OAAO,CAAC,KAAK;oBAClC;gBACF;;QACF;yBAAG,EAAE;IAEL,MAAM,0BAA0B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,OAAO;YACjD,IAAI;gBACF,gBAAgB;gBAEhB,mBAAmB,OAAO,GAAG,IAAI;gBAEjC,MAAM,WAAW,MAAM,MAAM,wBAAwB;oBACnD,QAAQ;oBACR,SAAS;wBAAE,gBAAgB;oBAAmB;oBAC9C,MAAM,KAAK,SAAS,CAAC;wBAAE;oBAAO;oBAC9B,QAAQ,mBAAmB,OAAO,CAAC,MAAM;gBAC3C;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;gBAC1D;gBAEA,MAAM,mBAAiC,MAAM,SAAS,IAAI;gBAC1D,kBAAkB;gBAElB;iEAAS,CAAC,YACR,UAAU,GAAG;yEAAC,CAAC,OACb,KAAK,EAAE,KAAK,MACR;oCAAE,GAAG,IAAI;oCAAE,QAAQ;gCAAY,IAC/B,KAAK,EAAE,KAAK,MACZ;oCAAE,GAAG,IAAI;oCAAE,QAAQ;gCAAY,IAC/B;;;gBAIR,8CAA8C;gBAC9C,MAAM,kBAAkB,MAAM,sBAC5B,iBAAiB,GAAG;iEAAC,CAAC,SAAW,OAAO,KAAK;iEAC7C,iBAAiB,GAAG;iEAAC,CAAC,SAAW,OAAO,WAAW;;gBAGrD,wCAAwC;gBACxC,MAAM,mBAAmB,kBAAkB,iBAAiB;YAC9D,EAAE,OAAO,OAAO;gBACd,IAAI,iBAAiB,SAAS,MAAM,IAAI,KAAK,cAAc;oBACzD,QAAQ,GAAG,CAAC;oBACZ;gBACF;gBAEA;iEAAS,CAAC,YACR,UAAU,GAAG;yEAAC,CAAC,OACb,KAAK,MAAM,KAAK,gBACZ;oCACE,GAAG,IAAI;oCACP,QAAQ;oCACR,MAAM,GAAG,KAAK,IAAI,CAAC,SAAS,EAC1B,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO,OACjD,CAAC,CAAC;gCACL,IACA;;;YAGV;QACF;oDAAG,EAAE;IAEL,MAAM,wBAAwB,OAC5B,OACA;QAEA,IAAI;YACF,MAAM,gBAAgB,MAAM,GAAG,CAAC,CAAC,IAAM,EAAE,IAAI,IAAI,IAAI,CAAC;YACtD,MAAM,sBAAsB,YAAY,GAAG,CAAC,CAAC,IAAM,EAAE,IAAI,IAAI,IAAI,CAAC;YAClE,MAAM,SAAS,KAAK,EAAE;YAEtB,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,OAAO;oBACP,SAAS;oBACT,QAAQ;gBACV;gBACA,QAAQ,mBAAmB,OAAO,EAAE;YACtC;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,WAAY,MAAM,SAAS,IAAI;YACrC,UAAU,SAAS,EAAE;YACrB,OAAO,SAAS,EAAE,EAAE,oBAAoB;QAC1C,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,SAAS,MAAM,IAAI,KAAK,cAAc;gBACzD,OAAO;YACT;YAEA,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO;QACT;IACF;IAEA,MAAM,6BAA6B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAC3C,OACE,OACA,aACA;YAEA,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,cAAc;oBACzC,QAAQ;oBACR,SAAS;wBAAE,gBAAgB;oBAAmB;oBAC9C,MAAM,KAAK,SAAS,CAAC;wBACnB,QAAQ;4BAAE;4BAAO;wBAAY;wBAC7B,WAAW;oBACb;oBACA,QAAQ,mBAAmB,OAAO,EAAE;gBACtC;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;gBAC1D;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM,OAAO,KAAK,IAAI;gBAEtB;oEAAkB,CAAC,OACjB,KAAK,GAAG;4EAAC,CAAC,SACR,OAAO,KAAK,KAAK,QAAQ;oCAAE,GAAG,MAAM;oCAAE,MAAM;gCAAK,IAAI;;;gBAIzD;oEAAS,CAAC,YACR,UAAU,GAAG;4EAAC,CAAC,OACb,KAAK,EAAE,KAAK,CAAC,MAAM,EAAE,OAAO,GACxB;oCAAE,GAAG,IAAI;oCAAE,QAAQ;gCAAY,IAC/B;;;gBAIR,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,IAAI,iBAAiB,SAAS,MAAM,IAAI,KAAK,cAAc;oBACzD,OAAO;gBACT;gBAEA;oEAAS,CAAC,YACR,UAAU,GAAG;4EAAC,CAAC,OACb,KAAK,EAAE,KAAK,CAAC,MAAM,EAAE,OAAO,GACxB;oCACE,GAAG,IAAI;oCACP,QAAQ;oCACR,MAAM,GAAG,KAAK,IAAI,CAAC,SAAS,EAC1B,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO,OACjD,CAAC,CAAC;gCACL,IACA;;;gBAGR,OAAO;YACT;QACF;uDACA,EAAE,CAAC,kEAAkE;;IAGvE,MAAM,yBAAyB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDACvC,OAAO,SAAuB;YAC5B,IAAI;gBACF,QAAQ,GAAG,CAAC;gBAEZ,MAAM,aAAa,QAChB,MAAM;2EAAC,CAAC,SAAW,OAAO,IAAI;0EAC9B,GAAG;2EAAC,CAAC,SAAW,OAAO,IAAI;;gBAE9B,IAAI,WAAW,MAAM,KAAK,GAAG;oBAC3B,MAAM,IAAI,MAAM;gBAClB;gBAEA,IAAI,CAAC,QAAQ;oBACX,MAAM,IAAI,MAAM;gBAClB;gBAEA,uDAAuD;gBACvD,MAAM,cAAc,QACjB,MAAM;4EAAC,CAAC,SAAW,OAAO,IAAI;2EAC9B,GAAG;4EAAC,CAAC,SAAW,CAAC;4BAChB,YAAY,OAAO,IAAI;4BACvB,aAAa,OAAO,WAAW;wBACjC,CAAC;;gBAEH,MAAM,gBAAgB;oBACpB,WAAW,aAAa;oBACxB,SAAS;oBACT,SAAS;oBACT,UAAU;gBACZ;gBAEA,uCAAuC;gBACvC,IAAI;oBACF,MAAM,MAAM,yBAAyB;wBACnC,QAAQ;wBACR,SAAS;4BAAE,gBAAgB;wBAAmB;wBAC9C,MAAM,KAAK,SAAS,CAAC;4BAAE,SAAS;wBAAO;oBACzC;oBACA,QAAQ,GAAG,CAAC;gBACd,EAAE,OAAO,SAAS;oBAChB,QAAQ,KAAK,CAAC,0CAA0C;gBACxD,yDAAyD;gBAC3D;gBAEA,QAAQ,GAAG,CAAC,iCAAiC;gBAE7C,MAAM,WAAW,MAAM,MAAM,GAAG,YAAY,aAAa,CAAC,EAAE;oBAC1D,QAAQ;oBACR,SAAS;wBAAE,gBAAgB;oBAAmB;oBAC9C,MAAM,KAAK,SAAS,CAAC;oBACrB,QAAQ,mBAAmB,OAAO,EAAE;gBACtC;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;oBACrC,MAAM,IAAI,MAAM,CAAC,eAAe,EAAE,SAAS,MAAM,CAAC,GAAG,EAAE,WAAW;gBACpE;gBAEA,MAAM,SAAS,MAAM,SAAS,IAAI;gBAClC,QAAQ,GAAG,CAAC,0BAA0B;gBAEtC,cAAc,OAAO,MAAM;gBAC3B,iBAAiB,OAAO,cAAc;gBAEtC;gEAAS,CAAC,YAAc;+BACnB;4BACH;gCACE,IAAI;gCACJ,MAAM;gCACN,QAAQ;4BACV;yBACD;;gBAED,+CAA+C;gBAC/C;gEAAW;wBACT,gBAAgB;wBAChB,0BAA0B;oBAC5B;+DAAG;YACL,EAAE,OAAO,OAAO;gBACd,IAAI,iBAAiB,SAAS,MAAM,IAAI,KAAK,cAAc;oBACzD;gBACF;gBAEA,QAAQ,KAAK,CAAC,0BAA0B;gBACxC;gEAAS,CAAC,YAAc;+BACnB;4BACH;gCACE,IAAI;gCACJ,MAAM,CAAC,wBAAwB,EAC7B,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO,QAChD;gCACF,QAAQ;4BACV;yBACD;;YACH;QACF;mDACA,EAAE;IAGJ,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDACnC,OACE,SACA,QACA;YAEA,MAAM,aAAqB,QAAQ,GAAG;mEAAC,CAAC,SAAW,CAAC;wBAClD,IAAI,CAAC,MAAM,EAAE,OAAO,KAAK,EAAE;wBAC3B,MAAM,CAAC,YAAY,EAAE,OAAO,KAAK,EAAE;wBACnC,QAAQ;oBACV,CAAC;;YAED;wDAAS,CAAC,YAAc;2BACnB,UAAU,MAAM;oEAAC,CAAC,IAAM,EAAE,EAAE,KAAK;;2BACjC;wBACH;4BAAE,IAAI;4BAAK,MAAM;4BAAyB,QAAQ;wBAAc;qBACjE;;YAED,MAAM,kBAAgC,EAAE;YAExC,KAAK,MAAM,cAAc,QAAS;gBAChC;4DAAS,CAAC,YACR,UAAU,GAAG;oEAAC,CAAC,OACb,KAAK,EAAE,KAAK,CAAC,MAAM,EAAE,WAAW,KAAK,EAAE,GACnC;oCAAE,GAAG,IAAI;oCAAE,QAAQ;gCAAc,IACjC;;;gBAIR,MAAM,OAAO,MAAM,2BACjB,WAAW,KAAK,EAChB,WAAW,WAAW,EACtB;gBAGF,IAAI,MAAM;oBACR,gBAAgB,IAAI,CAAC;wBAAE,GAAG,UAAU;wBAAE;oBAAK;gBAC7C;YACF;YAEA;wDAAS,CAAC,YACR,UAAU,GAAG;gEAAC,CAAC,OACb,KAAK,EAAE,KAAK,MAAM;gCAAE,GAAG,IAAI;gCAAE,QAAQ;4BAAY,IAAI;;;YAIzD,iEAAiE;YACjE,IAAI,gBAAgB,MAAM,GAAG,KAAK,QAAQ;gBACxC;4DAAW;wBACT,uBAAuB,iBAAiB;oBAC1C;2DAAG;YACL,OAAO;gBACL,QAAQ,IAAI,CAAC;YACf;QACF;+CACA;QAAC;QAA4B;KAAuB;IAGtD,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDACpC,OAAO;YACL,iDAAiD;YACjD,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM;gBAC7B,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,EAAE,mBAAmB,EAAE,WAAW,EAAE,GAAG,MAAM,SAAS,IAAI;oBAEhE,IAAI,qBAAqB;wBACvB,MAAM,CAAC,sDAAsD,EAAE,YAAY,MAAM,CAAC,4DAA4D,CAAC;wBAC/I;oBACF;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,0CAA0C;YAC5C;YAEA,gBAAgB;YAChB,oBAAoB;YACpB,kBAAkB,EAAE;YACpB,UAAU;YAEV,wCAAmC;gBACjC,aAAa,OAAO,CAAC,iBAAiB;gBACtC,aAAa;YACf;YAEA,MAAM,eAAuB;gBAC3B;oBAAE,IAAI;oBAAK,MAAM;oBAAmB,QAAQ;gBAAc;gBAC1D;oBAAE,IAAI;oBAAK,MAAM;oBAAqB,QAAQ;gBAAU;gBACxD;oBAAE,IAAI;oBAAK,MAAM;oBAAyB,QAAQ;gBAAU;aAC7D;YACD,SAAS;YAET,IAAI;gBACF;6DAAS,CAAC,YACR,UAAU,GAAG;qEAAC,CAAC,OACb,KAAK,EAAE,KAAK,MAAM;oCAAE,GAAG,IAAI;oCAAE,QAAQ;gCAAc,IAAI;;;gBAG3D,MAAM,wBAAwB;YAChC,EAAE,OAAO,OAAO;gBACd,IAAI,iBAAiB,SAAS,MAAM,IAAI,KAAK,cAAc;oBACzD,QAAQ,GAAG,CAAC;oBACZ;gBACF;gBAEA;6DAAS,CAAC,YACR,UAAU,GAAG;qEAAC,CAAC,OACb,KAAK,MAAM,KAAK,gBACZ;oCACE,GAAG,IAAI;oCACP,QAAQ;oCACR,MAAM,GAAG,KAAK,IAAI,CAAC,SAAS,EAC1B,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO,OACjD,CAAC,CAAC;gCACL,IACA;;;YAGV;QACF;gDACA;QAAC;KAAwB;IAG3B,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE;YACpC,0BAA0B;QAC5B;8CAAG,EAAE;IAEL,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE;YACrC,0BAA0B;YAC1B;wDAAW;oBACT,gBAAgB;oBAChB,oBAAoB;oBACpB,kBAAkB,EAAE;oBACpB,SAAS,EAAE;oBACX,cAAc;oBACd,UAAU;gBACZ;uDAAG;QACL;+CAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,kJAAA,CAAA,UAAY;gBAEX,cAAc;gBACd,YAAY;eAFP;;;;;YAIN,kCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAoB,cAAc;4BAAc,OAAO;;;;;;sCAExD,6LAAC;4BACC,cAAc;4BACd,gBAAgB;;;;;;;;;;;;;;;;;0BAMxB,6LAAC,6JAAA,CAAA,UAAuB;gBAEtB,QAAQ;gBACR,SAAS;gBACT,mBAAmB;gBACnB,YAAY;gBACZ,eAAe;eALV;;;;;;;;;;;AASb;GA1cwB;;QACL,8HAAA,CAAA,cAAW;;;MADN", "debugId": null}}]}