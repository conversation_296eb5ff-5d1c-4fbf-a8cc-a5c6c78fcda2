{"[project]/src/components/mvpblocks/ContentDisplayPanel.tsx [app-client] (ecmascript, next/dynamic entry)": {"id": "[project]/src/components/mvpblocks/ContentDisplayPanel.tsx [app-client] (ecmascript, next/dynamic entry)", "files": ["static/chunks/_1330d85d._.js", "static/chunks/[root-of-the-server]__29ec26bc._.js", "static/chunks/[next]_internal_font_google_geist_mono_7e1f0032_module_b1e4eef1.css", "static/chunks/src_components_mvpblocks_ContentDisplayPanel_tsx_f077eb8e._.js"]}, "[project]/src/components/mvpblocks/TaskProgressSidebar.tsx [app-client] (ecmascript, next/dynamic entry)": {"id": "[project]/src/components/mvpblocks/TaskProgressSidebar.tsx [app-client] (ecmascript, next/dynamic entry)", "files": ["static/chunks/_ec983ae7._.js", "static/chunks/src_components_mvpblocks_TaskProgressSidebar_tsx_f077eb8e._.js"]}}