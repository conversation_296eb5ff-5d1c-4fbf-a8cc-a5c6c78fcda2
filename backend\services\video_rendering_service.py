import os
import subprocess
import tempfile
import uuid
import re
import shutil
import logging
import textwrap
from typing import Optional, List, Tuple
from dotenv import load_dotenv
from config.queue_config import get_queue_config

# Load environment variables like x.py does
dotenv_path = os.path.join(os.path.dirname(__file__), "..", ".env")
load_dotenv(dotenv_path=dotenv_path)

logger = logging.getLogger(__name__)


class VideoRenderingService:
    """Service for rendering Manim videos and extracting scene information."""

    def __init__(self):
        self.config = get_queue_config()
        self.video_dir = "media/videos"
        os.makedirs(self.video_dir, exist_ok=True)

    def render_manim_video(self, code_str: str, video_id: str) -> Tuple[Optional[str], Optional[str]]:
        """
        Render a Manim video from code string - following x.py implementation.

        Args:
            code_str: The Manim code to render
            video_id: Unique identifier for this video

        Returns:
            Tuple of (video_path, py_file_path) or (None, None) if failed
        """
        # Extract class name from code
        class_name = self._extract_scene_name(code_str)

        # Create temporary Python file
        py_file = f"{video_id}.py"
        code_str = textwrap.dedent(code_str.strip())

        with open(py_file, "w", encoding="utf-8") as f:
            f.write(code_str)

        try:
            subprocess.run(
                [
                    "manim",
                    "-qk",
                    "-o",
                    f"{video_id}.mp4",
                    py_file,
                    class_name,
                    "--media_dir",
                    "media",
                ],
                capture_output=True,
                text=True,
                check=True,
            )
        except subprocess.CalledProcessError as e:
            logger.error(f"🚫 Manim render failed! {e.stdout} {e.stderr}")
            if os.path.exists(py_file):
                os.remove(py_file)
            return None, None

        # Find the rendered video - following x.py pattern
        for root, _, files in os.walk(self.video_dir):
            for file in files:
                if file == f"{video_id}.mp4":
                    return os.path.join(root, file), py_file

        logger.error("❌ Video not found in media/videos")
        if os.path.exists(py_file):
            os.remove(py_file)
        return None, None

    def _extract_scene_name(self, code_str: str) -> str:
        """Extract the scene class name from Manim code."""
        match = re.search(r"class\s+(\w+)\s*\(.*Scene.*\):", code_str)
        if match:
            return match.group(1)
        
        # Fallback: look for any class that inherits from Scene
        lines = code_str.splitlines()
        for line in lines:
            if line.strip().startswith("class ") and "Scene" in line:
                class_name = line.split("(")[0].replace("class ", "").strip()
                if class_name:
                    return class_name
        
        return "AutoScene"

    def _find_rendered_video(self, video_id: str) -> Optional[str]:
        """Find the rendered video file in the media directory."""
        for root, _, files in os.walk(self.video_dir):
            for file in files:
                if file == f"{video_id}.mp4":
                    return os.path.join(root, file)
        return None

    def get_media_duration(self, path: str) -> float:
        """Get the duration of a media file using ffprobe."""
        try:
            result = subprocess.run(
                [
                    "ffprobe",
                    "-v", "error",
                    "-show_entries", "format=duration",
                    "-of", "default=noprint_wrappers=1:nokey=1",
                    path,
                ],
                capture_output=True,
                text=True,
                check=True,
            )
            return float(result.stdout.strip())
        except Exception as e:
            logger.warning(f"Could not get duration for {path}: {e}")
            return 0.0

    def cleanup_temp_files(self, py_file: str, video_id: str):
        """Clean up temporary files created during rendering."""
        try:
            # Remove Python file
            if py_file and os.path.exists(py_file):
                os.remove(py_file)
            
            # Remove video folder
            video_folder = os.path.join("media", "videos", video_id)
            if os.path.exists(video_folder):
                shutil.rmtree(video_folder)
                
        except Exception as e:
            logger.warning(f"Failed to cleanup temp files for {video_id}: {e}")


# Global service instance
_video_rendering_service: Optional[VideoRenderingService] = None


def get_video_rendering_service() -> VideoRenderingService:
    """Get the global VideoRenderingService instance."""
    global _video_rendering_service
    if _video_rendering_service is None:
        _video_rendering_service = VideoRenderingService()
    return _video_rendering_service
