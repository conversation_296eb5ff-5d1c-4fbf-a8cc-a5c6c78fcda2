{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from 'react';\r\nimport { cva, type VariantProps } from 'class-variance-authority';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst badgeVariants = cva(\r\n  'inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          'border-transparent bg-primary text-primary-foreground hover:bg-primary/80',\r\n        secondary:\r\n          'border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80',\r\n        destructive:\r\n          'border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80',\r\n        outline: 'text-foreground',\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default',\r\n    },\r\n  },\r\n);\r\n\r\nexport interface BadgeProps\r\n  extends React.HTMLAttributes<HTMLDivElement>,\r\n    VariantProps<typeof badgeVariants> {}\r\n\r\nfunction Badge({ className, variant, ...props }: BadgeProps) {\r\n  return (\r\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\r\n  );\r\n}\r\n\r\nexport { Badge, badgeVariants };\r\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Dialog({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\r\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\r\n}\r\n\r\nfunction DialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\r\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\r\n}\r\n\r\nfunction DialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\r\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\r\n}\r\n\r\nfunction DialogClose({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\r\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\r\n}\r\n\r\nfunction DialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\r\n  return (\r\n    <DialogPrimitive.Overlay\r\n      data-slot=\"dialog-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogContent({\r\n  className,\r\n  children,\r\n  showCloseButton = true,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\r\n  showCloseButton?: boolean\r\n}) {\r\n  return (\r\n    <DialogPortal data-slot=\"dialog-portal\">\r\n      <DialogOverlay />\r\n      <DialogPrimitive.Content\r\n        data-slot=\"dialog-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        {showCloseButton && (\r\n          <DialogPrimitive.Close\r\n            data-slot=\"dialog-close\"\r\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\r\n          >\r\n            <XIcon />\r\n            <span className=\"sr-only\">Close</span>\r\n          </DialogPrimitive.Close>\r\n        )}\r\n      </DialogPrimitive.Content>\r\n    </DialogPortal>\r\n  )\r\n}\r\n\r\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-header\"\r\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-footer\"\r\n      className={cn(\r\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\r\n  return (\r\n    <DialogPrimitive.Title\r\n      data-slot=\"dialog-title\"\r\n      className={cn(\"text-lg leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\r\n  return (\r\n    <DialogPrimitive.Description\r\n      data-slot=\"dialog-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Dialog,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogOverlay,\r\n  DialogPortal,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,6LAAC,qKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,6LAAC,mMAAA,CAAA,QAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;MAhCS;AAkCT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 253, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Label({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  return (\r\n    <LabelPrimitive.Root\r\n      data-slot=\"label\"\r\n      className={cn(\r\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 287, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\r\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Select({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\r\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\r\n}\r\n\r\nfunction SelectGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\r\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\r\n}\r\n\r\nfunction SelectValue({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\r\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\r\n}\r\n\r\nfunction SelectTrigger({\r\n  className,\r\n  size = \"default\",\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\r\n  size?: \"sm\" | \"default\"\r\n}) {\r\n  return (\r\n    <SelectPrimitive.Trigger\r\n      data-slot=\"select-trigger\"\r\n      data-size={size}\r\n      className={cn(\r\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <SelectPrimitive.Icon asChild>\r\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\r\n      </SelectPrimitive.Icon>\r\n    </SelectPrimitive.Trigger>\r\n  )\r\n}\r\n\r\nfunction SelectContent({\r\n  className,\r\n  children,\r\n  position = \"popper\",\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\r\n  return (\r\n    <SelectPrimitive.Portal>\r\n      <SelectPrimitive.Content\r\n        data-slot=\"select-content\"\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\r\n          position === \"popper\" &&\r\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n          className\r\n        )}\r\n        position={position}\r\n        {...props}\r\n      >\r\n        <SelectScrollUpButton />\r\n        <SelectPrimitive.Viewport\r\n          className={cn(\r\n            \"p-1\",\r\n            position === \"popper\" &&\r\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\r\n          )}\r\n        >\r\n          {children}\r\n        </SelectPrimitive.Viewport>\r\n        <SelectScrollDownButton />\r\n      </SelectPrimitive.Content>\r\n    </SelectPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction SelectLabel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\r\n  return (\r\n    <SelectPrimitive.Label\r\n      data-slot=\"select-label\"\r\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SelectItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\r\n  return (\r\n    <SelectPrimitive.Item\r\n      data-slot=\"select-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\r\n        <SelectPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </SelectPrimitive.ItemIndicator>\r\n      </span>\r\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n    </SelectPrimitive.Item>\r\n  )\r\n}\r\n\r\nfunction SelectSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\r\n  return (\r\n    <SelectPrimitive.Separator\r\n      data-slot=\"select-separator\"\r\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SelectScrollUpButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollUpButton\r\n      data-slot=\"select-scroll-up-button\"\r\n      className={cn(\r\n        \"flex cursor-default items-center justify-center py-1\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronUpIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollUpButton>\r\n  )\r\n}\r\n\r\nfunction SelectScrollDownButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollDownButton\r\n      data-slot=\"select-scroll-down-button\"\r\n      className={cn(\r\n        \"flex cursor-default items-center justify-center py-1\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronDownIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollDownButton>\r\n  )\r\n}\r\n\r\nexport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectScrollDownButton,\r\n  SelectScrollUpButton,\r\n  SelectSeparator,\r\n  SelectTrigger,\r\n  SelectValue,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 536, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\r\n  return (\r\n    <textarea\r\n      data-slot=\"textarea\"\r\n      className={cn(\r\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Textarea }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 567, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/actions/quiz.actions.ts"], "sourcesContent": ["\"use server\"\r\n\r\nimport { db } from \"@/lib/db\";\r\nimport { quizSchema, Quiz } from \"@/lib/quizSchema\";\r\n\r\nexport async function createQuiz(data: {\r\n  userId: string;\r\n  prompt: string;\r\n  quiz: Quiz;\r\n}) {\r\n  const parseResult = quizSchema.safeParse(data.quiz);\r\n\r\n  if (!parseResult.success) {\r\n    console.error(\"Invalid quiz format\", parseResult.error.format());\r\n    throw new Error(\"Invalid quiz format\");\r\n  }\r\n\r\n  const validatedQuiz: Quiz = parseResult.data;\r\n\r\n  // Convert correctAnswer to string for each question\r\n  const quizForDb = {\r\n    ...validatedQuiz,\r\n    questions: validatedQuiz.questions.map((q) => ({\r\n      ...q,\r\n      correctAnswer: String(q.correctAnswer),\r\n    })),\r\n  };\r\n\r\n  return await db.entries.create({\r\n    data: {\r\n      userId: data.userId,\r\n      prompt: data.prompt,\r\n      quiz: quizForDb,\r\n    },\r\n  });\r\n}\r\n\r\nexport async function deleteQuiz(id: string) {\r\n  const entry = await db.entries.findUnique({\r\n    where: { id },\r\n  });\r\n  \r\n  if (!entry) {\r\n    throw new Error(\"Quiz not found\");\r\n  }\r\n\r\n  const updated = await db.entries.update({\r\n    where: { id },\r\n    data: {\r\n      quiz: null,\r\n      hasGiven: false,\r\n      marks: null,\r\n    },\r\n  });\r\n\r\n  return {\r\n    success: true,\r\n    message: \"Quiz removed successfully\",\r\n    updated,\r\n  };\r\n}\r\n\r\nexport async function answeredQuiz(data: {\r\n  id: string;\r\n  marks: number;\r\n}) {\r\n  const entry = await db.entries.findUnique({\r\n    where: { id: data.id },\r\n  });\r\n\r\n  if (!entry) {\r\n    throw new Error(\"Quiz not found\");\r\n  }\r\n\r\n  const updated = await db.entries.update({\r\n    where: { id: data.id },\r\n    data: {\r\n      hasGiven: true,\r\n      marks: data.marks,\r\n    },\r\n  });\r\n\r\n  return {\r\n    success: true,\r\n    message: \"Quiz marked as answered\",\r\n    updated,\r\n  };\r\n}\r\n\r\nexport async function getQuizById(id: string) {\r\n  const entry = await db.entries.findUnique({\r\n    where: { id },\r\n  });\r\n\r\n  if (!entry) {\r\n    return null;\r\n  }\r\n\r\n  if (!entry.quiz) {\r\n    return null;\r\n  }\r\n\r\n  // If given\r\n  if (entry.hasGiven) {\r\n    return {\r\n      ...entry,\r\n      quiz: {\r\n        ...entry.quiz,\r\n        questions: entry.quiz.questions.map((q) => ({\r\n          ...q,\r\n          correctAnswer: String(q.correctAnswer), // Ensure correctAnswer is a string\r\n        })),\r\n      },\r\n    };\r\n  }\r\n\r\n  return entry;\r\n}\r\n\r\nexport async function getUserEntries(userId: string) {\r\n  const entries = await db.entries.findMany({\r\n    where: { userId },\r\n    orderBy: { createdAt: \"desc\" },\r\n  });\r\n\r\n  return entries.map((entry) => ({\r\n    ...entry,\r\n    quiz: entry.quiz\r\n      ? {\r\n          ...entry.quiz,\r\n          questions: entry.quiz.questions.map((q) => ({\r\n            ...q,\r\n            correctAnswer: String(q.correctAnswer), // Ensure correctAnswer is a string\r\n          })),\r\n        }\r\n      : null,\r\n  }));\r\n}"], "names": [], "mappings": ";;;;;;IAuHsB,iBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 583, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/hooks/useAuthUser.ts"], "sourcesContent": ["// hooks/useAuthUser.ts\r\nimport { useEffect, useState } from \"react\";\r\n\r\nexport function useAuthUser() {\r\n  const [user, setUser] = useState<any>(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [unauthenticated, setUnauthenticated] = useState(false);\r\n\r\n  useEffect(() => {\r\n    async function fetchUser() {\r\n      try {\r\n        const res = await fetch(\"/api/user\");\r\n        if (res.status === 401) {\r\n          setUnauthenticated(true);\r\n          setUser(null);\r\n        } else {\r\n          const data = await res.json();\r\n          setUser(data.user);\r\n        }\r\n      } catch (err) {\r\n        console.error(\"Failed to fetch user\", err);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    }\r\n\r\n    fetchUser();\r\n  }, []);\r\n\r\n  return { user, loading, unauthenticated };\r\n}\r\n"], "names": [], "mappings": "AAAA,uBAAuB;;;;AACvB;;;AAEO,SAAS;;IACd,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IACtC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,eAAe;gBACb,IAAI;oBACF,MAAM,MAAM,MAAM,MAAM;oBACxB,IAAI,IAAI,MAAM,KAAK,KAAK;wBACtB,mBAAmB;wBACnB,QAAQ;oBACV,OAAO;wBACL,MAAM,OAAO,MAAM,IAAI,IAAI;wBAC3B,QAAQ,KAAK,IAAI;oBACnB;gBACF,EAAE,OAAO,KAAK;oBACZ,QAAQ,KAAK,CAAC,wBAAwB;gBACxC,SAAU;oBACR,WAAW;gBACb;YACF;YAEA;QACF;gCAAG,EAAE;IAEL,OAAO;QAAE;QAAM;QAAS;IAAgB;AAC1C;GA3BgB", "debugId": null}}, {"offset": {"line": 632, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/app/%28app%29/library/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Bad<PERSON> } from \"@/components/ui/badge\";\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from \"@/components/ui/dialog\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport { Progress } from \"@/components/ui/progress\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport { Textarea } from \"@/components/ui/textarea\";\r\nimport { TooltipProvider } from \"@/components/ui/tooltip\";\r\nimport { Entries } from \"@prisma/client\";\r\nimport {\r\n  AlertCircle,\r\n  Brain,\r\n  CheckCircle2,\r\n  Clock,\r\n  Copy,\r\n  Download,\r\n  Edit,\r\n  Facebook,\r\n  Linkedin,\r\n  Loader2,\r\n  MessageSquare,\r\n  MoreVertical,\r\n  Play,\r\n  Search,\r\n  Share,\r\n  ThumbsDown,\r\n  ThumbsUp,\r\n  Trash2,\r\n  Twitter,\r\n  VideoIcon,\r\n} from \"lucide-react\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { toast } from \"sonner\";\r\nimport { getUserEntries } from \"@/actions/quiz.actions\";\r\nimport { useAuthUser } from \"@/hooks/useAuthUser\";\r\n\r\nexport default function VideoLibrary() {\r\n  const { user, loading } = useAuthUser();\r\n  console.log(\"User in VideoLibrary:\", user);\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n  const [sortBy, setSortBy] = useState(\"newest\");\r\n  const [filterBy, setFilterBy] = useState(\"all\");\r\n  const [entries, setEntries] = useState<Entries[]>([]);\r\n  const [sharingVideo, setSharingVideo] = useState<any>(null);\r\n  const [editingVideo, setEditingVideo] = useState<any>(null);\r\n\r\n  useEffect(() => {\r\n    const getEntries = async () => {\r\n      if (!user || loading) return;\r\n      try {\r\n        const data = await getUserEntries(user.id);\r\n        setEntries(data);\r\n      } catch (error) {\r\n        console.error(\"Error fetching entries:\", error);\r\n        toast.error(\"Failed to load videos. Please try again later.\");\r\n      }\r\n    };\r\n    getEntries();\r\n  }, [loading]);\r\n\r\n\r\n  // const handleDeleteVideo = (id: number) => {\r\n  //   setVideos(videos.filter((video) => video.id !== id));\r\n  //   toast.success(\"Video deleted successfully\");\r\n  // };\r\n\r\n  // const handleEditVideo = (updatedVideo: Video) => {\r\n  //   setVideos(\r\n  //     videos.map((video) =>\r\n  //       video.id === updatedVideo.id ? updatedVideo : video\r\n  //     )\r\n  //   );\r\n  //   setEditingVideo(null);\r\n  //   toast.success(\"Video details updated successfully\");\r\n  // };\r\n\r\n  // const handleLikeVideo = (id: number) => {\r\n  //   setVideos(\r\n  //     videos.map((video) =>\r\n  //       video.id === id\r\n  //         ? {\r\n  //             ...video,\r\n  //             isLiked: !video.isLiked,\r\n  //             isDisliked: video.isLiked ? video.isDisliked : false,\r\n  //             likes: video.isLiked ? video.likes - 1 : video.likes + 1,\r\n  //             dislikes: video.isLiked\r\n  //               ? video.dislikes\r\n  //               : video.isDisliked\r\n  //               ? video.dislikes - 1\r\n  //               : video.dislikes,\r\n  //           }\r\n  //         : video\r\n  //     )\r\n  //   );\r\n  // };\r\n\r\n  // const handleDislikeVideo = (id: number) => {\r\n  //   setVideos(\r\n  //     videos.map((video) =>\r\n  //       video.id === id\r\n  //         ? {\r\n  //             ...video,\r\n  //             isDisliked: !video.isDisliked,\r\n  //             isLiked: video.isDisliked ? video.isLiked : false,\r\n  //             dislikes: video.isDisliked\r\n  //               ? video.dislikes - 1\r\n  //               : video.dislikes + 1,\r\n  //             likes: video.isDisliked\r\n  //               ? video.likes\r\n  //               : video.isLiked\r\n  //               ? video.likes - 1\r\n  //               : video.likes,\r\n  //           }\r\n  //         : video\r\n  //     )\r\n  //   );\r\n  // };\r\n\r\n  // const formatDate = (dateString: string) => {\r\n  //   const now = new Date();\r\n  //   const date = new Date(dateString);\r\n  //   const diffInHours = Math.floor(\r\n  //     (now.getTime() - date.getTime()) / (1000 * 60 * 60)\r\n  //   );\r\n\r\n  //   if (diffInHours < 1) return \"Just now\";\r\n  //   if (diffInHours < 24)\r\n  //     return `${diffInHours} hour${diffInHours !== 1 ? \"s\" : \"\"} ago`;\r\n  //   if (diffInHours < 168)\r\n  //     return `${Math.floor(diffInHours / 24)} day${\r\n  //       Math.floor(diffInHours / 24) !== 1 ? \"s\" : \"\"\r\n  //     } ago`;\r\n  //   if (diffInHours < 720)\r\n  //     return `${Math.floor(diffInHours / 168)} week${\r\n  //       Math.floor(diffInHours / 168) !== 1 ? \"s\" : \"\"\r\n  //     } ago`;\r\n  //   return date.toLocaleDateString(\"en-US\", {\r\n  //     year: \"numeric\",\r\n  //     month: \"short\",\r\n  //     day: \"numeric\",\r\n  //   });\r\n  // };\r\n\r\n  const formatViews = (views: number) => {\r\n    if (views === 0) return \"No views\";\r\n    if (views < 1000) return `${views} view${views !== 1 ? \"s\" : \"\"}`;\r\n    if (views < 1000000) return `${(views / 1000).toFixed(1)}K views`;\r\n    return `${(views / 1000000).toFixed(1)}M views`;\r\n  };\r\n\r\n  function handleDeleteVideo(id: string): void {\r\n    throw new Error(\"Function not implemented.\");\r\n  }\r\n\r\n  // const getStatusIcon = (status: string) => {\r\n  //   switch (status) {\r\n  //     case \"completed\":\r\n  //       return <CheckCircle2 className=\"h-4 w-4 text-green-500\" />;\r\n  //     case \"generating\":\r\n  //       return <Loader2 className=\"h-4 w-4 text-blue-500 animate-spin\" />;\r\n  //     case \"processing\":\r\n  //       return <Clock className=\"h-4 w-4 text-yellow-500\" />;\r\n  //     case \"failed\":\r\n  //       return <AlertCircle className=\"h-4 w-4 text-red-500\" />;\r\n  //     default:\r\n  //       return null;\r\n  //   }\r\n  // };\r\n\r\n  // If loading then show loading animation\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex items-center justify-center h-screen\">\r\n        <Loader2 className=\"h-8 w-8 animate-spin text-primary\" />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <TooltipProvider>\r\n      <div className=\"flex-1 space-y-6 p-4 md:p-8 pt-6 animate-fade-in\">\r\n        {/* Header */}\r\n        <div className=\"flex items-center justify-between\">\r\n          <div className=\"space-y-1\">\r\n            <h1 className=\"text-3xl font-bold tracking-tight\">Video Library</h1>\r\n            <p className=\"text-muted-foreground\">\r\n              Manage and view all your AI-generated videos in one place. Use\r\n              the search and filters to find specific videos quickly.\r\n            </p>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Search and Filters - YouTube Style */}\r\n        <div className=\"flex flex-col space-y-4 lg:flex-row lg:items-center lg:space-x-4 lg:space-y-0\">\r\n          {/* Search Bar */}\r\n          <div className=\"relative flex-1 max-w-full\">\r\n            <Search className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground\" />\r\n            <Input\r\n              placeholder=\"Search your videos...\"\r\n              value={searchQuery}\r\n              onChange={(e) => setSearchQuery(e.target.value)}\r\n              className=\"pl-10 h-10 bg-background border-border focus:border-primary rounded-full\"\r\n            />\r\n          </div>\r\n\r\n          {/* Filter and Sort Controls */}\r\n          <div className=\"flex items-center space-x-2\">\r\n            <Select value={filterBy} onValueChange={setFilterBy}>\r\n              <SelectTrigger className=\"w-[140px] h-10 rounded-full\">\r\n                <SelectValue />\r\n              </SelectTrigger>\r\n              <SelectContent>\r\n                <SelectItem value=\"all\">All Styles</SelectItem>\r\n                <SelectItem value=\"realistic\">Realistic</SelectItem>\r\n                <SelectItem value=\"artistic\">Artistic</SelectItem>\r\n                <SelectItem value=\"cinematic\">Cinematic</SelectItem>\r\n                <SelectItem value=\"animated\">Animated</SelectItem>\r\n              </SelectContent>\r\n            </Select>\r\n\r\n            <Select value={sortBy} onValueChange={setSortBy}>\r\n              <SelectTrigger className=\"w-[160px] h-10 rounded-full\">\r\n                <SelectValue />\r\n              </SelectTrigger>\r\n              <SelectContent>\r\n                <SelectItem value=\"newest\">Upload date (newest)</SelectItem>\r\n                <SelectItem value=\"oldest\">Upload date (oldest)</SelectItem>\r\n                <SelectItem value=\"views\">View count</SelectItem>\r\n                <SelectItem value=\"likes\">Most liked</SelectItem>\r\n                <SelectItem value=\"title\">Title (A-Z)</SelectItem>\r\n                <SelectItem value=\"duration\">Duration</SelectItem>\r\n              </SelectContent>\r\n            </Select>\r\n          </div>\r\n        </div>\r\n\r\n        {/* YouTube-Style Video Grid */}\r\n        <div className=\"grid gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-3\">\r\n          {entries.map((video, index) => (\r\n            <div\r\n              key={video.id}\r\n              className=\"group cursor-pointer animate-slide-up\"\r\n              style={{ animationDelay: `${index * 0.05}s` }}\r\n            >\r\n              {/* Video Thumbnail Container */}\r\n              <div className=\"relative aspect-video mb-3 overflow-hidden rounded-xl bg-muted shadow-sm hover:shadow-md transition-shadow duration-200\">\r\n                <video\r\n                  src={video.videoUrl || \"/placeholder.mp4\"}\r\n                  className=\"h-full w-full object-cover transition-transform duration-200 group-hover:scale-105\"\r\n                  autoPlay\r\n                  loop\r\n                  muted\r\n                />\r\n\r\n                {/* Video Duration Overlay */}\r\n                <div className=\"absolute bottom-2 right-2\">\r\n                  <Badge\r\n                    variant=\"secondary\"\r\n                    className=\"bg-black/80 text-white border-0 text-xs font-medium px-2 py-1 rounded\"\r\n                  >\r\n                    {/* Random between 1.30 mins to 3 mins */}\r\n                    {Math.floor(Math.random() * 90 + 90)} s\r\n                  </Badge>\r\n                </div>\r\n\r\n                {/* Status Indicators for Generating/Processing Videos */}\r\n                {/* {(video.status === \"generating\" ||\r\n                  video.status === \"processing\") && (\r\n                  <div className=\"absolute inset-0 bg-black/60 flex items-center justify-center\">\r\n                    <div className=\"text-center text-white space-y-3 p-4\">\r\n                      <div className=\"flex items-center justify-center space-x-2\">\r\n                        {getStatusIcon(video.status)}\r\n                        <span className=\"text-sm font-medium capitalize\">\r\n                          {video.status}...\r\n                        </span>\r\n                      </div>\r\n                      <div className=\"w-32 mx-auto\">\r\n                        <Progress\r\n                          value={video.progress || 0}\r\n                          className=\"h-2 bg-white/20\"\r\n                        />\r\n                      </div>\r\n                      <div className=\"text-xs opacity-80\">\r\n                        {video.progress || 0}% complete\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                )} */}\r\n\r\n                {/* Status Icon for Completed Videos */}\r\n                {/* {video.status === \"completed\" && (\r\n                  <div className=\"absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200\">\r\n                    {getStatusIcon(video.status)}\r\n                  </div>\r\n                )} */}\r\n\r\n                {/* Play Button Overlay */}\r\n                {/* {video.status === \"completed\" && (\r\n                  <div className=\"absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center\">\r\n                    <div className=\"bg-white/90 rounded-full p-4 transform scale-90 group-hover:scale-100 transition-transform duration-200 shadow-lg\">\r\n                      <Play className=\"h-6 w-6 text-black fill-current ml-0.5\" />\r\n                    </div>\r\n                  </div>\r\n                )} */}\r\n\r\n                {/* Context Menu */}\r\n                <div className=\"absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200\">\r\n                  <DropdownMenu>\r\n                    <DropdownMenuTrigger asChild>\r\n                      <Button\r\n                        variant=\"ghost\"\r\n                        size=\"sm\"\r\n                        className=\"h-8 w-8 p-0 bg-black/60 hover:bg-black/80 text-white rounded-full\"\r\n                      >\r\n                        <MoreVertical className=\"h-4 w-4\" />\r\n                      </Button>\r\n                    </DropdownMenuTrigger>\r\n                    <DropdownMenuContent\r\n                      align=\"end\"\r\n                      className=\"animate-scale-in\"\r\n                    >\r\n                      <DropdownMenuItem>\r\n                        <Play className=\"mr-2 h-4 w-4\" />\r\n                        Play\r\n                      </DropdownMenuItem>\r\n                      <DropdownMenuItem>\r\n                        <Download className=\"mr-2 h-4 w-4\" />\r\n                        Download\r\n                      </DropdownMenuItem>\r\n                      <DropdownMenuItem onClick={() => setEditingVideo(video)}>\r\n                        <Edit className=\"mr-2 h-4 w-4\" />\r\n                        Edit details\r\n                      </DropdownMenuItem>\r\n                      <DropdownMenuItem\r\n                        onClick={() => handleDeleteVideo(video.id)}\r\n                        className=\"text-destructive focus:text-destructive\"\r\n                      >\r\n                        <Trash2 className=\"mr-2 h-4 w-4\" />\r\n                        Delete\r\n                      </DropdownMenuItem>\r\n                      <DropdownMenuItem onClick={() => setSharingVideo(video)}>\r\n                        <Share className=\"mr-2 h-4 w-4\" />\r\n                        Share\r\n                      </DropdownMenuItem>\r\n                    </DropdownMenuContent>\r\n                  </DropdownMenu>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Video Information - YouTube Style */}\r\n              <div className=\"space-y-2\">\r\n                {/* Creator Avatar and Title */}\r\n                <div className=\"flex space-x-3\">\r\n                  <div className=\"flex-1 min-w-0\">\r\n                    {/* Title */}\r\n                    <h3 className=\"font-medium text-md leading-5 line-clamp-2 group-hover:text-primary transition-colors mb-1\">\r\n                      {video.quiz?.title || \"Untitled Video\"}\r\n                    </h3>\r\n\r\n                    {/* Views and Date */}\r\n                    <div className=\"flex items-center space-x-1 text-xs text-muted-foreground\">\r\n                      <span>{formatViews(\r\n                        Math.random() * 1000\r\n                      )}</span>\r\n                      {/* {video.views > 0 && (\r\n                        <>\r\n                          <span>•</span>\r\n                          <span>{formatDate(video.createdAt)}</span>\r\n                        </>\r\n                      )} */}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Description */}\r\n                {video.quiz?.description && (\r\n                  <p className=\"text-xs text-muted-foreground line-clamp-2 leading-4\">\r\n                    {video.quiz?.description}\r\n                  </p>\r\n                )}\r\n\r\n                {/* Engagement and Metadata */}\r\n                <div className=\"flex items-center justify-between\">\r\n                  {/* Engagement Stats */}\r\n                  {/* {video.status === \"completed\" && video.views > 0 && (\r\n                    <div className=\"flex items-center space-x-3\">\r\n                      <button\r\n                        onClick={(e) => {\r\n                          e.stopPropagation();\r\n                          handleLikeVideo(video.id);\r\n                        }}\r\n                        className={`flex items-center space-x-1 text-xs transition-colors ${\r\n                          video.isLiked\r\n                            ? \"text-blue-600\"\r\n                            : \"text-muted-foreground hover:text-blue-600\"\r\n                        }`}\r\n                      >\r\n                        <ThumbsUp\r\n                          className={`h-3 w-3 ${\r\n                            video.isLiked ? \"fill-current\" : \"\"\r\n                          }`}\r\n                        />\r\n                        <span>{video.likes}</span>\r\n                      </button>\r\n\r\n                      <button\r\n                        onClick={(e) => {\r\n                          e.stopPropagation();\r\n                          handleDislikeVideo(video.id);\r\n                        }}\r\n                        className={`flex items-center space-x-1 text-xs transition-colors ${\r\n                          video.isDisliked\r\n                            ? \"text-red-600\"\r\n                            : \"text-muted-foreground hover:text-red-600\"\r\n                        }`}\r\n                      >\r\n                        <ThumbsDown\r\n                          className={`h-3 w-3 ${\r\n                            video.isDisliked ? \"fill-current\" : \"\"\r\n                          }`}\r\n                        />\r\n                        <span>{video.dislikes}</span>\r\n                      </button>\r\n                    </div>\r\n                  )} */}\r\n\r\n                  {/* Metadata Badges */}\r\n                  <div className=\"flex items-center space-x-1\">\r\n                    <Badge variant=\"outline\" className=\"text-xs px-1.5 py-0.5\">\r\n                      {video.quiz?.questions.length || 0}\r\n                    </Badge>\r\n                  </div>\r\n                </div>\r\n                {/* Take Quiz Button */}\r\n                <div className=\"mt-3\">\r\n                  <Button\r\n                    size=\"sm\"\r\n                    disabled={video.hasGiven}\r\n                    className={`w-full transition-all duration-200 ${\r\n                      !video.hasGiven\r\n                        ? \"bg-muted text-muted-foreground cursor-not-allowed hover:bg-muted\"\r\n                        : \"bg-primary text-primary-foreground hover:bg-primary/90\"\r\n                    }`}\r\n                    onClick={(e) => {\r\n                      e.stopPropagation();\r\n                      if (!video.hasGiven) {\r\n                        // Navigate to quiz page for this video\r\n                        window.location.href = `/quiz/${video.id}`;\r\n                      }\r\n                    }}\r\n                  >\r\n                    <Brain className=\"h-3 w-3 mr-1\" />\r\n                    {!video.hasGiven\r\n                      ? \"Take Quiz\"\r\n                      : \"Quiz Unavailable\"}\r\n                  </Button>\r\n                </div>\r\n\r\n                {/* Status for generating videos */}\r\n                {/* {(video.status === \"generating\" ||\r\n                  video.status === \"processing\") && (\r\n                  <div className=\"flex items-center space-x-2 text-xs\">\r\n                    <Badge variant=\"secondary\" className=\"text-xs\">\r\n                      {video.status === \"generating\"\r\n                        ? \"Generating\"\r\n                        : \"Processing\"}\r\n                    </Badge>\r\n                    <span className=\"text-muted-foreground\">\r\n                      {video.progress || 0}% complete\r\n                    </span>\r\n                  </div>\r\n                )} */}\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Empty State */}\r\n        {entries.length === 0 && (\r\n          <div className=\"flex flex-col items-center justify-center py-16\">\r\n            <div className=\"text-center space-y-4\">\r\n              <div className=\"mx-auto h-20 w-20 rounded-full bg-muted flex items-center justify-center\">\r\n                <VideoIcon className=\"h-10 w-10 text-muted-foreground\" />\r\n              </div>\r\n              <div className=\"space-y-2\">\r\n                <h3 className=\"text-xl font-semibold\">No videos found</h3>\r\n                <p className=\"text-muted-foreground max-w-md\">\r\n                  {searchQuery\r\n                    ? `No videos match \"${searchQuery}\". Try adjusting your search terms or filters.`\r\n                    : \"You haven't generated any videos yet. Create your first video to get started!\"}\r\n                </p>\r\n              </div>\r\n              {!searchQuery && (\r\n                <Button asChild className=\"mt-4\">\r\n                  <a href=\"/generate\">\r\n                    <VideoIcon className=\"mr-2 h-4 w-4\" />\r\n                    Generate Your First Video\r\n                  </a>\r\n                </Button>\r\n              )}\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Edit Video Modal */}\r\n        {/* <Dialog\r\n          open={!!editingVideo}\r\n          onOpenChange={() => setEditingVideo(null)}\r\n        >\r\n          <DialogContent className=\"sm:max-w-[600px] max-h-[80vh] overflow-y-auto animate-scale-in\">\r\n            <DialogHeader>\r\n              <DialogTitle>Edit Video Details</DialogTitle>\r\n              <DialogDescription>\r\n                Update your video's title, description, and other metadata.\r\n              </DialogDescription>\r\n            </DialogHeader>\r\n            {editingVideo && (\r\n              <div className=\"grid gap-6 py-4\">\r\n                <div className=\"aspect-video w-full overflow-hidden rounded-lg bg-muted border\">\r\n                  <img\r\n                    src={editingVideo.thumbnail || \"/placeholder.svg\"}\r\n                    alt={editingVideo.title}\r\n                    className=\"h-full w-full object-cover\"\r\n                  />\r\n                </div>\r\n\r\n                <div className=\"grid gap-4\">\r\n                  <div className=\"grid gap-2\">\r\n                    <Label htmlFor=\"edit-title\">Title</Label>\r\n                    <Input\r\n                      id=\"edit-title\"\r\n                      value={editingVideo.title}\r\n                      onChange={(e) =>\r\n                        setEditingVideo({\r\n                          ...editingVideo,\r\n                          title: e.target.value,\r\n                        })\r\n                      }\r\n                      className=\"transition-colors focus:border-primary\"\r\n                      placeholder=\"Enter video title...\"\r\n                    />\r\n                  </div>\r\n\r\n                  <div className=\"grid gap-2\">\r\n                    <Label htmlFor=\"edit-description\">Description</Label>\r\n                    <Textarea\r\n                      id=\"edit-description\"\r\n                      value={editingVideo.description || \"\"}\r\n                      onChange={(e) =>\r\n                        setEditingVideo({\r\n                          ...editingVideo,\r\n                          description: e.target.value,\r\n                        })\r\n                      }\r\n                      rows={4}\r\n                      className=\"resize-none transition-colors focus:border-primary\"\r\n                      placeholder=\"Add a description for your video...\"\r\n                    />\r\n                  </div>\r\n\r\n                  <div className=\"grid gap-2\">\r\n                    <Label htmlFor=\"edit-prompt\">Original Prompt</Label>\r\n                    <Textarea\r\n                      id=\"edit-prompt\"\r\n                      value={editingVideo.prompt}\r\n                      onChange={(e) =>\r\n                        setEditingVideo({\r\n                          ...editingVideo,\r\n                          prompt: e.target.value,\r\n                        })\r\n                      }\r\n                      rows={3}\r\n                      className=\"resize-none transition-colors focus:border-primary\"\r\n                      placeholder=\"The prompt used to generate this video...\"\r\n                    />\r\n                  </div>\r\n\r\n                  <div className=\"grid grid-cols-3 gap-4 p-4 bg-muted/30 rounded-lg\">\r\n                    <div className=\"text-center\">\r\n                      <div className=\"text-2xl font-bold\">\r\n                        {formatViews(editingVideo.views)}\r\n                      </div>\r\n                      <div className=\"text-sm text-muted-foreground\">Views</div>\r\n                    </div>\r\n                    <div className=\"text-center\">\r\n                      <div className=\"text-2xl font-bold\">\r\n                        {editingVideo.likes}\r\n                      </div>\r\n                      <div className=\"text-sm text-muted-foreground\">Likes</div>\r\n                    </div>\r\n                    <div className=\"text-center\">\r\n                      <div className=\"text-2xl font-bold\">\r\n                        {editingVideo.dislikes}\r\n                      </div>\r\n                      <div className=\"text-sm text-muted-foreground\">\r\n                        Dislikes\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )}\r\n            <DialogFooter>\r\n              <Button variant=\"outline\" onClick={() => setEditingVideo(null)}>\r\n                Cancel\r\n              </Button>\r\n              <Button\r\n                onClick={() => editingVideo && handleEditVideo(editingVideo)}\r\n              >\r\n                Save Changes\r\n              </Button>\r\n            </DialogFooter>\r\n          </DialogContent>\r\n        </Dialog> */}\r\n        \r\n        <Dialog\r\n          open={!!sharingVideo}\r\n          onOpenChange={() => setSharingVideo(null)}\r\n        >\r\n          <DialogContent className=\"sm:max-w-[600px] max-h-[80vh] overflow-y-auto animate-scale-in\">\r\n            <DialogHeader>\r\n              <DialogTitle>Share Video</DialogTitle>\r\n              <DialogDescription>\r\n                Share \"{sharingVideo?.title}\" with others\r\n              </DialogDescription>\r\n            </DialogHeader>\r\n            {sharingVideo && (\r\n              <div className=\"space-y-4\">\r\n                <div className=\"aspect-video w-full overflow-hidden rounded-lg bg-muted border\">\r\n                  <video\r\n                    src={sharingVideo.videoUrl || \"/placeholder.mp4\"}\r\n                    className=\"h-full w-full object-cover\"\r\n                    controls\r\n                    autoPlay\r\n                    loop\r\n                  />\r\n                </div>\r\n\r\n                <div className=\"space-y-2\">\r\n                  <Label>Share Link</Label>\r\n                  <div className=\"flex space-x-2\">\r\n                    <Input\r\n                      value={`https://clarif.ai/watch/${sharingVideo.id}`}\r\n                      readOnly\r\n                      className=\"flex-1\"\r\n                    />\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"sm\"\r\n                      onClick={() => {\r\n                        navigator.clipboard.writeText(\r\n                          `https://clarif.ai/watch/${sharingVideo.id}`\r\n                        );\r\n                        toast.success(\"Video link copied to clipboard!\");\r\n                      }}\r\n                    >\r\n                      <Copy className=\"h-4 w-4\" />\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"space-y-3\">\r\n                  <Label>Share on Social Media</Label>\r\n                  <div className=\"grid grid-cols-2 gap-3\">\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className=\"justify-start\"\r\n                      onClick={() => {\r\n                        const url = `https://clarif.ai/watch/${sharingVideo.id}`;\r\n                        const text = `Check out this amazing AI-generated video: ${sharingVideo.title}`;\r\n                        window.open(\r\n                          `https://twitter.com/intent/tweet?text=${encodeURIComponent(\r\n                            text\r\n                          )}&url=${encodeURIComponent(url)}`,\r\n                          \"_blank\"\r\n                        );\r\n                      }}\r\n                    >\r\n                      <Twitter className=\"h-4 w-4 mr-2 text-blue-500\" />\r\n                      Twitter\r\n                    </Button>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className=\"justify-start\"\r\n                      onClick={() => {\r\n                        const url = `https://clarif.ai/watch/${sharingVideo.id}`;\r\n                        window.open(\r\n                          `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(\r\n                            url\r\n                          )}`,\r\n                          \"_blank\"\r\n                        );\r\n                      }}\r\n                    >\r\n                      <Facebook className=\"h-4 w-4 mr-2 text-blue-600\" />\r\n                      Facebook\r\n                    </Button>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className=\"justify-start\"\r\n                      onClick={() => {\r\n                        const url = `https://clarif.ai/watch/${sharingVideo.id}`;\r\n                        const text = `Check out this AI-generated video: ${sharingVideo.title}`;\r\n                        window.open(\r\n                          `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(\r\n                            url\r\n                          )}`,\r\n                          \"_blank\"\r\n                        );\r\n                      }}\r\n                    >\r\n                      <Linkedin className=\"h-4 w-4 mr-2 text-blue-700\" />\r\n                      LinkedIn\r\n                    </Button>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className=\"justify-start\"\r\n                      onClick={() => {\r\n                        const url = `https://clarif.ai/watch/${sharingVideo.id}`;\r\n                        const text = `Check out this amazing AI-generated video: ${sharingVideo.title} ${url}`;\r\n                        window.open(\r\n                          `https://wa.me/?text=${encodeURIComponent(text)}`,\r\n                          \"_blank\"\r\n                        );\r\n                      }}\r\n                    >\r\n                      <MessageSquare className=\"h-4 w-4 mr-2 text-green-600\" />\r\n                      WhatsApp\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"space-y-2\">\r\n                  <Label>Embed Code</Label>\r\n                  <Textarea\r\n                    value={`<iframe src=\"https://clarif.ai/embed/${sharingVideo.id}\" width=\"560\" height=\"315\" frameborder=\"0\" allowfullscreen></iframe>`}\r\n                    readOnly\r\n                    rows={3}\r\n                    className=\"text-xs\"\r\n                  />\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    size=\"sm\"\r\n                    onClick={() => {\r\n                      navigator.clipboard.writeText(\r\n                        `<iframe src=\"https://clarif.ai/embed/${sharingVideo.id}\" width=\"560\" height=\"315\" frameborder=\"0\" allowfullscreen></iframe>`\r\n                      );\r\n                      toast.success(\"Embed code copied to clipboard!\");\r\n                    }}\r\n                  >\r\n                    <Copy className=\"h-4 w-4 mr-2\" />\r\n                    Copy Embed Code\r\n                  </Button>\r\n                </div>\r\n              </div>\r\n            )}\r\n            <DialogFooter>\r\n              <Button variant=\"outline\" onClick={() => setSharingVideo(null)}>\r\n                Close\r\n              </Button>\r\n            </DialogFooter>\r\n          </DialogContent>\r\n        </Dialog>\r\n      </div>\r\n    </TooltipProvider>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAQA;AAMA;AACA;AAEA;AAOA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAsBA;AACA;AACA;AACA;;;AAxDA;;;;;;;;;;;;;;;AA0De,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IACpC,QAAQ,GAAG,CAAC,yBAAyB;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACpD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IACtD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAEtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;qDAAa;oBACjB,IAAI,CAAC,QAAQ,SAAS;oBACtB,IAAI;wBACF,MAAM,OAAO,MAAM,CAAA,GAAA,yJAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,EAAE;wBACzC,WAAW;oBACb,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,2BAA2B;wBACzC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACd;gBACF;;YACA;QACF;iCAAG;QAAC;KAAQ;IAGZ,8CAA8C;IAC9C,0DAA0D;IAC1D,iDAAiD;IACjD,KAAK;IAEL,qDAAqD;IACrD,eAAe;IACf,4BAA4B;IAC5B,4DAA4D;IAC5D,QAAQ;IACR,OAAO;IACP,2BAA2B;IAC3B,yDAAyD;IACzD,KAAK;IAEL,4CAA4C;IAC5C,eAAe;IACf,4BAA4B;IAC5B,wBAAwB;IACxB,cAAc;IACd,wBAAwB;IACxB,uCAAuC;IACvC,oEAAoE;IACpE,wEAAwE;IACxE,sCAAsC;IACtC,iCAAiC;IACjC,mCAAmC;IACnC,qCAAqC;IACrC,kCAAkC;IAClC,cAAc;IACd,kBAAkB;IAClB,QAAQ;IACR,OAAO;IACP,KAAK;IAEL,+CAA+C;IAC/C,eAAe;IACf,4BAA4B;IAC5B,wBAAwB;IACxB,cAAc;IACd,wBAAwB;IACxB,6CAA6C;IAC7C,iEAAiE;IACjE,yCAAyC;IACzC,qCAAqC;IACrC,sCAAsC;IACtC,sCAAsC;IACtC,8BAA8B;IAC9B,gCAAgC;IAChC,kCAAkC;IAClC,+BAA+B;IAC/B,cAAc;IACd,kBAAkB;IAClB,QAAQ;IACR,OAAO;IACP,KAAK;IAEL,+CAA+C;IAC/C,4BAA4B;IAC5B,uCAAuC;IACvC,oCAAoC;IACpC,0DAA0D;IAC1D,OAAO;IAEP,4CAA4C;IAC5C,0BAA0B;IAC1B,uEAAuE;IACvE,2BAA2B;IAC3B,oDAAoD;IACpD,sDAAsD;IACtD,cAAc;IACd,2BAA2B;IAC3B,sDAAsD;IACtD,uDAAuD;IACvD,cAAc;IACd,8CAA8C;IAC9C,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,QAAQ;IACR,KAAK;IAEL,MAAM,cAAc,CAAC;QACnB,IAAI,UAAU,GAAG,OAAO;QACxB,IAAI,QAAQ,MAAM,OAAO,GAAG,MAAM,KAAK,EAAE,UAAU,IAAI,MAAM,IAAI;QACjE,IAAI,QAAQ,SAAS,OAAO,GAAG,CAAC,QAAQ,IAAI,EAAE,OAAO,CAAC,GAAG,OAAO,CAAC;QACjE,OAAO,GAAG,CAAC,QAAQ,OAAO,EAAE,OAAO,CAAC,GAAG,OAAO,CAAC;IACjD;IAEA,SAAS,kBAAkB,EAAU;QACnC,MAAM,IAAI,MAAM;IAClB;IAEA,8CAA8C;IAC9C,sBAAsB;IACtB,wBAAwB;IACxB,oEAAoE;IACpE,yBAAyB;IACzB,2EAA2E;IAC3E,yBAAyB;IACzB,8DAA8D;IAC9D,qBAAqB;IACrB,iEAAiE;IACjE,eAAe;IACf,qBAAqB;IACrB,MAAM;IACN,KAAK;IAEL,yCAAyC;IACzC,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,oNAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;;;;;;IAGzB;IAEA,qBACE,6LAAC,sIAAA,CAAA,kBAAe;kBACd,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;8BAQzC,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC,oIAAA,CAAA,QAAK;oCACJ,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,WAAU;;;;;;;;;;;;sCAKd,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCAAC,OAAO;oCAAU,eAAe;;sDACtC,6LAAC,qIAAA,CAAA,gBAAa;4CAAC,WAAU;sDACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;sDAEd,6LAAC,qIAAA,CAAA,gBAAa;;8DACZ,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAM;;;;;;8DACxB,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAY;;;;;;8DAC9B,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAW;;;;;;8DAC7B,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAY;;;;;;8DAC9B,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAW;;;;;;;;;;;;;;;;;;8CAIjC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,OAAO;oCAAQ,eAAe;;sDACpC,6LAAC,qIAAA,CAAA,gBAAa;4CAAC,WAAU;sDACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;sDAEd,6LAAC,qIAAA,CAAA,gBAAa;;8DACZ,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAS;;;;;;8DAC3B,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAS;;;;;;8DAC3B,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAQ;;;;;;8DAC1B,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAQ;;;;;;8DAC1B,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAQ;;;;;;8DAC1B,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOrC,6LAAC;oBAAI,WAAU;8BACZ,QAAQ,GAAG,CAAC,CAAC,OAAO,sBACnB,6LAAC;4BAEC,WAAU;4BACV,OAAO;gCAAE,gBAAgB,GAAG,QAAQ,KAAK,CAAC,CAAC;4BAAC;;8CAG5C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,KAAK,MAAM,QAAQ,IAAI;4CACvB,WAAU;4CACV,QAAQ;4CACR,IAAI;4CACJ,KAAK;;;;;;sDAIP,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;gDACJ,SAAQ;gDACR,WAAU;;oDAGT,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK;oDAAI;;;;;;;;;;;;sDA6CzC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,+IAAA,CAAA,eAAY;;kEACX,6LAAC,+IAAA,CAAA,sBAAmB;wDAAC,OAAO;kEAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,WAAU;sEAEV,cAAA,6LAAC,6NAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;;;;;;;;;;;kEAG5B,6LAAC,+IAAA,CAAA,sBAAmB;wDAClB,OAAM;wDACN,WAAU;;0EAEV,6LAAC,+IAAA,CAAA,mBAAgB;;kFACf,6LAAC,qMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGnC,6LAAC,+IAAA,CAAA,mBAAgB;;kFACf,6LAAC,6MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGvC,6LAAC,+IAAA,CAAA,mBAAgB;gEAAC,SAAS,IAAM,gBAAgB;;kFAC/C,6LAAC,8MAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGnC,6LAAC,+IAAA,CAAA,mBAAgB;gEACf,SAAS,IAAM,kBAAkB,MAAM,EAAE;gEACzC,WAAU;;kFAEV,6LAAC,6MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGrC,6LAAC,+IAAA,CAAA,mBAAgB;gEAAC,SAAS,IAAM,gBAAgB;;kFAC/C,6LAAC,uMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAS5C,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEAEb,6LAAC;wDAAG,WAAU;kEACX,MAAM,IAAI,EAAE,SAAS;;;;;;kEAIxB,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;sEAAM,YACL,KAAK,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;wCAavB,MAAM,IAAI,EAAE,6BACX,6LAAC;4CAAE,WAAU;sDACV,MAAM,IAAI,EAAE;;;;;;sDAKjB,6LAAC;4CAAI,WAAU;sDA6Cb,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAU,WAAU;8DAChC,MAAM,IAAI,EAAE,UAAU,UAAU;;;;;;;;;;;;;;;;sDAKvC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,UAAU,MAAM,QAAQ;gDACxB,WAAW,CAAC,mCAAmC,EAC7C,CAAC,MAAM,QAAQ,GACX,qEACA,0DACJ;gDACF,SAAS,CAAC;oDACR,EAAE,eAAe;oDACjB,IAAI,CAAC,MAAM,QAAQ,EAAE;wDACnB,uCAAuC;wDACvC,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE;oDAC5C;gDACF;;kEAEA,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAChB,CAAC,MAAM,QAAQ,GACZ,cACA;;;;;;;;;;;;;;;;;;;2BAtNL,MAAM,EAAE;;;;;;;;;;gBA8OlB,QAAQ,MAAM,KAAK,mBAClB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,2MAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;0CAEvB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAwB;;;;;;kDACtC,6LAAC;wCAAE,WAAU;kDACV,cACG,CAAC,iBAAiB,EAAE,YAAY,8CAA8C,CAAC,GAC/E;;;;;;;;;;;;4BAGP,CAAC,6BACA,6LAAC,qIAAA,CAAA,SAAM;gCAAC,OAAO;gCAAC,WAAU;0CACxB,cAAA,6LAAC;oCAAE,MAAK;;sDACN,6LAAC,2MAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;8BAwHlD,6LAAC,qIAAA,CAAA,SAAM;oBACL,MAAM,CAAC,CAAC;oBACR,cAAc,IAAM,gBAAgB;8BAEpC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;wBAAC,WAAU;;0CACvB,6LAAC,qIAAA,CAAA,eAAY;;kDACX,6LAAC,qIAAA,CAAA,cAAW;kDAAC;;;;;;kDACb,6LAAC,qIAAA,CAAA,oBAAiB;;4CAAC;4CACT,cAAc;4CAAM;;;;;;;;;;;;;4BAG/B,8BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,KAAK,aAAa,QAAQ,IAAI;4CAC9B,WAAU;4CACV,QAAQ;4CACR,QAAQ;4CACR,IAAI;;;;;;;;;;;kDAIR,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDACJ,OAAO,CAAC,wBAAwB,EAAE,aAAa,EAAE,EAAE;wDACnD,QAAQ;wDACR,WAAU;;;;;;kEAEZ,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS;4DACP,UAAU,SAAS,CAAC,SAAS,CAC3B,CAAC,wBAAwB,EAAE,aAAa,EAAE,EAAE;4DAE9C,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;wDAChB;kEAEA,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kDAKtB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS;4DACP,MAAM,MAAM,CAAC,wBAAwB,EAAE,aAAa,EAAE,EAAE;4DACxD,MAAM,OAAO,CAAC,2CAA2C,EAAE,aAAa,KAAK,EAAE;4DAC/E,OAAO,IAAI,CACT,CAAC,sCAAsC,EAAE,mBACvC,MACA,KAAK,EAAE,mBAAmB,MAAM,EAClC;wDAEJ;;0EAEA,6LAAC,2MAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;4DAA+B;;;;;;;kEAGpD,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS;4DACP,MAAM,MAAM,CAAC,wBAAwB,EAAE,aAAa,EAAE,EAAE;4DACxD,OAAO,IAAI,CACT,CAAC,6CAA6C,EAAE,mBAC9C,MACC,EACH;wDAEJ;;0EAEA,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAA+B;;;;;;;kEAGrD,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS;4DACP,MAAM,MAAM,CAAC,wBAAwB,EAAE,aAAa,EAAE,EAAE;4DACxD,MAAM,OAAO,CAAC,mCAAmC,EAAE,aAAa,KAAK,EAAE;4DACvE,OAAO,IAAI,CACT,CAAC,oDAAoD,EAAE,mBACrD,MACC,EACH;wDAEJ;;0EAEA,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAA+B;;;;;;;kEAGrD,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS;4DACP,MAAM,MAAM,CAAC,wBAAwB,EAAE,aAAa,EAAE,EAAE;4DACxD,MAAM,OAAO,CAAC,2CAA2C,EAAE,aAAa,KAAK,CAAC,CAAC,EAAE,KAAK;4DACtF,OAAO,IAAI,CACT,CAAC,oBAAoB,EAAE,mBAAmB,OAAO,EACjD;wDAEJ;;0EAEA,6LAAC,2NAAA,CAAA,gBAAa;gEAAC,WAAU;;;;;;4DAAgC;;;;;;;;;;;;;;;;;;;kDAM/D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,6LAAC,uIAAA,CAAA,WAAQ;gDACP,OAAO,CAAC,qCAAqC,EAAE,aAAa,EAAE,CAAC,oEAAoE,CAAC;gDACpI,QAAQ;gDACR,MAAM;gDACN,WAAU;;;;;;0DAEZ,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;oDACP,UAAU,SAAS,CAAC,SAAS,CAC3B,CAAC,qCAAqC,EAAE,aAAa,EAAE,CAAC,oEAAoE,CAAC;oDAE/H,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gDAChB;;kEAEA,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;0CAMzC,6LAAC,qIAAA,CAAA,eAAY;0CACX,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS,IAAM,gBAAgB;8CAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9E;GA1tBwB;;QACI,8HAAA,CAAA,cAAW;;;KADf", "debugId": null}}]}