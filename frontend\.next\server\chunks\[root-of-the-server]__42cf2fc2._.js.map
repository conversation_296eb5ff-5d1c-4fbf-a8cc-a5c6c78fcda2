{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from \"@prisma/client\";\r\n\r\ndeclare global {\r\n  var prisma: PrismaClient | undefined;\r\n}\r\n\r\nexport const db = globalThis.prisma || new PrismaClient();\r\n\r\nif (process.env.NODE_ENV !== \"production\") {\r\n  globalThis.prisma = db;\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAMO,MAAM,KAAK,WAAW,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEvD,wCAA2C;IACzC,WAAW,MAAM,GAAG;AACtB", "debugId": null}}, {"offset": {"line": 99, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/lib/auth.ts"], "sourcesContent": ["import { PrismaAdapter } from \"@auth/prisma-adapter\";\r\nimport NextAuth from \"next-auth\";\r\nimport Google from \"next-auth/providers/google\";\r\nimport { db } from \"./db\";\r\nimport GitHubProvider from \"next-auth/providers/github\";\r\nimport Resend from \"next-auth/providers/resend\";\r\n\r\n// eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-unused-vars\r\nasync function sendVerificationRequest(params: any) {\r\n  const { identifier: to, provider, url } = params;\r\n  const { host } = new URL(url);\r\n  const res = await fetch(\"https://api.resend.com/emails\", {\r\n    method: \"POST\",\r\n    headers: {\r\n      Authorization: `Bearer ${provider.apiKey}`,\r\n      \"Content-Type\": \"application/json\",\r\n    },\r\n    body: JSON.stringify({\r\n      from: provider.from,\r\n      to,\r\n      subject: `Sign in to ${host}`,\r\n      html: html({ url }),\r\n      text: text({ url, host }),\r\n    }),\r\n  });\r\n\r\n  if (!res.ok)\r\n    throw new Error(\"Resend error: \" + JSON.stringify(await res.json()));\r\n}\r\n\r\nfunction html(params: { url: string}) {\r\n  const { url } = params;\r\n\r\n  return `<!DOCTYPE html>\r\n<html lang=\"en\">\r\n  <head>\r\n    <meta charset=\"UTF-8\" />\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\r\n    <title>Verify Your Email</title>\r\n  </head>\r\n\r\n  <body\r\n    style=\"\r\n      margin: 0;\r\n      padding: 0;\r\n      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\r\n      background-color: #f3f4f6;\r\n    \"\r\n  >\r\n    <table\r\n      cellpadding=\"0\"\r\n      cellspacing=\"0\"\r\n      border=\"0\"\r\n      width=\"100%\"\r\n      style=\"min-width: 100%; background-color: #f3f4f6\"\r\n    >\r\n      <tr>\r\n        <td align=\"center\" style=\"padding: 40px 0\">\r\n          <table\r\n            cellpadding=\"0\"\r\n            cellspacing=\"0\"\r\n            border=\"0\"\r\n            width=\"600\"\r\n            style=\"\r\n              max-width: 600px;\r\n              background-color: #ffffff;\r\n              border-radius: 16px;\r\n              overflow: hidden;\r\n              box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\r\n            \"\r\n          >\r\n            <tr>\r\n              <td\r\n                style=\"\r\n                  background-color: #3b82f6;\r\n                  padding: 40px 0;\r\n                  text-align: center;\r\n                \"\r\n              >\r\n                <h1\r\n                  style=\"\r\n                    color: #ffffff;\r\n                    font-size: 28px;\r\n                    font-weight: 700;\r\n                    margin: 0;\r\n                    text-transform: uppercase;\r\n                    letter-spacing: 2px;\r\n                  \"\r\n                >\r\n                  Verify Your Email\r\n                </h1>\r\n              </td>\r\n            </tr>\r\n            <tr>\r\n              <td style=\"padding: 40px\">\r\n                <p\r\n                  style=\"\r\n                    color: #4b5563;\r\n                    font-size: 16px;\r\n                    line-height: 1.6;\r\n                    margin-bottom: 24px;\r\n                    text-align: center;\r\n                  \"\r\n                >\r\n                  Thanks for signing up! We're excited to have you on board. To\r\n                  get started, please verify your email address by clicking the\r\n                  button below.\r\n                </p>\r\n                <table\r\n                  cellpadding=\"0\"\r\n                  cellspacing=\"0\"\r\n                  border=\"0\"\r\n                  width=\"100%\"\r\n                  style=\"margin-bottom: 32px\"\r\n                >\r\n                  <tr>\r\n                    <td align=\"center\">\r\n                      <a\r\n                        href=\"${url}\"\r\n                        style=\"\r\n                          display: inline-block;\r\n                          background-color: #3b82f6;\r\n                          color: #ffffff;\r\n                          font-size: 16px;\r\n                          font-weight: 600;\r\n                          text-decoration: none;\r\n                          padding: 12px 32px;\r\n                          border-radius: 8px;\r\n                          transition: background-color 0.3s ease;\r\n                          text-transform: uppercase;\r\n                          letter-spacing: 1px;\r\n                        \"\r\n                        >Verify your email</a\r\n                      >\r\n                    </td>\r\n                  </tr>\r\n                </table>\r\n                <p\r\n                  style=\"\r\n                    color: #6b7280;\r\n                    font-size: 14px;\r\n                    line-height: 1.6;\r\n                    margin-bottom: 0;\r\n                    text-align: center;\r\n                  \"\r\n                >\r\n                  If you didn't create an account, no further action is\r\n                  required.\r\n                </p>\r\n              </td>\r\n            </tr>\r\n            <tr>\r\n              <td\r\n                style=\"\r\n                  background-color: #f9fafb;\r\n                  padding: 24px;\r\n                  text-align: center;\r\n                \"\r\n              >\r\n                <p style=\"color: #9ca3af; font-size: 12px; margin: 0\">\r\n                  © 2025 Acme Inc. All rights reserved.\r\n                </p>\r\n              </td>\r\n            </tr>\r\n          </table>\r\n        </td>\r\n      </tr>\r\n    </table>\r\n  </body>\r\n</html>\r\n`;\r\n}\r\n\r\nfunction text({ url, host }: { url: string; host: string }) {\r\n  return `Sign in to ${host}\\n${url}\\n\\n`;\r\n}\r\n\r\nexport const {\r\n  handlers: { GET, POST },\r\n  signIn,\r\n  signOut,\r\n  auth,\r\n} = NextAuth({\r\n  adapter: PrismaAdapter(db),\r\n  session: { strategy: \"jwt\" },\r\n  providers: [\r\n    Google({\r\n      clientId: process.env.AUTH_GOOGLE_ID,\r\n      clientSecret: process.env.AUTH_GOOGLE_SECRET,\r\n      authorization: {\r\n        params: {\r\n          prompt: \"consent\",\r\n          access_type: \"offline\",\r\n          response_type: \"code\",\r\n        },\r\n      },\r\n      allowDangerousEmailAccountLinking: true,\r\n    }),\r\n    Resend({\r\n      apiKey: process.env.AUTH_RESEND_KEY!, \r\n      from: 'Acme <<EMAIL>>',  // If you are using Resend and do not have a domain\r\n      // from: \"Acme Inc. <<EMAIL>>\", // For personal domains\r\n      // sendVerificationRequest, // For customised emails\r\n    }),\r\n    GitHubProvider({\r\n      clientId: process.env.GITHUB_ID,\r\n      clientSecret: process.env.GITHUB_SECRET,\r\n    }),\r\n  ],\r\n  callbacks: {\r\n    jwt({ token, user }) {\r\n      if (user) {\r\n        token.id = user.id;\r\n      }\r\n      return token;\r\n    },\r\n    session({ session, token }) {\r\n      session.user.id = token.id as string;\r\n      return session;\r\n    },\r\n  },\r\n  pages: {\r\n    verifyRequest: \"/verify-request\",\r\n  },\r\n});"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AAAA;AACA;AAAA;AACA;AACA;AAAA;AACA;AAAA;;;;;;;AAEA,iGAAiG;AACjG,eAAe,wBAAwB,MAAW;IAChD,MAAM,EAAE,YAAY,EAAE,EAAE,QAAQ,EAAE,GAAG,EAAE,GAAG;IAC1C,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI,IAAI;IACzB,MAAM,MAAM,MAAM,MAAM,iCAAiC;QACvD,QAAQ;QACR,SAAS;YACP,eAAe,CAAC,OAAO,EAAE,SAAS,MAAM,EAAE;YAC1C,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC;YACnB,MAAM,SAAS,IAAI;YACnB;YACA,SAAS,CAAC,WAAW,EAAE,MAAM;YAC7B,MAAM,KAAK;gBAAE;YAAI;YACjB,MAAM,KAAK;gBAAE;gBAAK;YAAK;QACzB;IACF;IAEA,IAAI,CAAC,IAAI,EAAE,EACT,MAAM,IAAI,MAAM,mBAAmB,KAAK,SAAS,CAAC,MAAM,IAAI,IAAI;AACpE;AAEA,SAAS,KAAK,MAAsB;IAClC,MAAM,EAAE,GAAG,EAAE,GAAG;IAEhB,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAqFoB,EAAE,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoDpC,CAAC;AACD;AAEA,SAAS,KAAK,EAAE,GAAG,EAAE,IAAI,EAAiC;IACxD,OAAO,CAAC,WAAW,EAAE,KAAK,EAAE,EAAE,IAAI,IAAI,CAAC;AACzC;AAEO,MAAM,EACX,UAAU,EAAE,GAAG,EAAE,IAAI,EAAE,EACvB,MAAM,EACN,OAAO,EACP,IAAI,EACL,GAAG,CAAA,GAAA,uJAAA,CAAA,UAAQ,AAAD,EAAE;IACX,SAAS,CAAA,GAAA,sJAAA,CAAA,gBAAa,AAAD,EAAE,kHAAA,CAAA,KAAE;IACzB,SAAS;QAAE,UAAU;IAAM;IAC3B,WAAW;QACT,CAAA,GAAA,uJAAA,CAAA,UAAM,AAAD,EAAE;YACL,UAAU,QAAQ,GAAG,CAAC,cAAc;YACpC,cAAc,QAAQ,GAAG,CAAC,kBAAkB;YAC5C,eAAe;gBACb,QAAQ;oBACN,QAAQ;oBACR,aAAa;oBACb,eAAe;gBACjB;YACF;YACA,mCAAmC;QACrC;QACA,CAAA,GAAA,uJAAA,CAAA,UAAM,AAAD,EAAE;YACL,QAAQ,QAAQ,GAAG,CAAC,eAAe;YACnC,MAAM;QAGR;QACA,CAAA,GAAA,uJAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,SAAS;YAC/B,cAAc,QAAQ,GAAG,CAAC,aAAa;QACzC;KACD;IACD,WAAW;QACT,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACjB,IAAI,MAAM;gBACR,MAAM,EAAE,GAAG,KAAK,EAAE;YACpB;YACA,OAAO;QACT;QACA,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YACxB,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE;YAC1B,OAAO;QACT;IACF;IACA,OAAO;QACL,eAAe;IACjB;AACF", "debugId": null}}, {"offset": {"line": 340, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/actions/user.actions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { db } from \"@/lib/db\";\r\n\r\nexport async function getUser(uid: string) {\r\n  const user = await db.user.findUnique({\r\n    where: {\r\n      id: uid,\r\n    },\r\n  });\r\n\r\n  return user;\r\n}\r\n\r\nexport async function getUserFromEntriesId(entryId: string) {\r\n  const entry = await db.entries.findUnique({\r\n    where: {\r\n      id: entryId,\r\n    },\r\n  });\r\n\r\n  if (!entry) {\r\n    return null;\r\n  }\r\n\r\n  const user = await db.user.findUnique({\r\n    where: {\r\n      id: entry.userId,\r\n    },\r\n  });\r\n\r\n  return user?.email;\r\n}"], "names": [], "mappings": ";;;;;;AAEA;;;;;AAEO,eAAe,QAAQ,GAAW;IACvC,MAAM,OAAO,MAAM,kHAAA,CAAA,KAAE,CAAC,IAAI,CAAC,UAAU,CAAC;QACpC,OAAO;YACL,IAAI;QACN;IACF;IAEA,OAAO;AACT;AAEO,eAAe,qBAAqB,OAAe;IACxD,MAAM,QAAQ,MAAM,kHAAA,CAAA,KAAE,CAAC,OAAO,CAAC,UAAU,CAAC;QACxC,OAAO;YACL,IAAI;QACN;IACF;IAEA,IAAI,CAAC,OAAO;QACV,OAAO;IACT;IAEA,MAAM,OAAO,MAAM,kHAAA,CAAA,KAAE,CAAC,IAAI,CAAC,UAAU,CAAC;QACpC,OAAO;YACL,IAAI,MAAM,MAAM;QAClB;IACF;IAEA,OAAO,MAAM;AACf;;;IA5BsB;IAUA;;AAVA,iPAAA;AAUA,iPAAA", "debugId": null}}, {"offset": {"line": 388, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/app/api/user/route.ts"], "sourcesContent": ["// app/api/user/route.ts\r\n\r\nimport { NextResponse } from \"next/server\";\r\nimport { auth } from \"@/lib/auth\";\r\nimport { getUser } from \"@/actions/user.actions\";\r\n\r\nexport async function GET() {\r\n  const session = await auth();\r\n\r\n  if (!session) {\r\n    return NextResponse.json({ user: null }, { status: 401 });\r\n  }\r\n\r\n  const dbUser = await getUser(session.user!.id!);\r\n  return NextResponse.json({ user: dbUser });\r\n}\r\n"], "names": [], "mappings": "AAAA,wBAAwB;;;;AAExB;AACA;AACA;;;;AAEO,eAAe;IACpB,MAAM,UAAU,MAAM,CAAA,GAAA,oHAAA,CAAA,OAAI,AAAD;IAEzB,IAAI,CAAC,SAAS;QACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,MAAM;QAAK,GAAG;YAAE,QAAQ;QAAI;IACzD;IAEA,MAAM,SAAS,MAAM,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD,EAAE,QAAQ,IAAI,CAAE,EAAE;IAC7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAAE,MAAM;IAAO;AAC1C", "debugId": null}}]}