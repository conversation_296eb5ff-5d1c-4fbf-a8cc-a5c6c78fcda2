{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/components/mvpblocks/InputSection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport {\r\n  <PERSON><PERSON>les,\r\n  Loader,\r\n  FileUp,\r\n  Terminal,\r\n  Monitor as MonitorIcon,\r\n  Figma,\r\n} from \"lucide-react\";\r\n\r\nconst EXAMPLE_ACTIONS = [\r\n  { icon: <Figma className=\"h-4 w-4\" />, text: \"Bayesian Theorem in ML\" },\r\n  { icon: <FileUp className=\"h-4 w-4\" />, text: \"Hidden Markov Models (HMM)\" },\r\n  {\r\n    icon: <MonitorIcon className=\"h-4 w-4\" />,\r\n    text: \"Gaussian Mixture Models\",\r\n  },\r\n  { icon: <Terminal className=\"h-4 w-4\" />, text: \"Linked Lists in DSA\" },\r\n  { icon: <FileUp className=\"h-4 w-4\" />, text: \"Binary Trees in DSA\" },\r\n  { icon: <Figma className=\"h-4 w-4\" />, text: \"Quadratic Equations in Maths\" },\r\n  {\r\n    icon: <FileUp className=\"h-4 w-4\" />,\r\n    text: \"Projectile Motion in Physics\",\r\n  },\r\n  {\r\n    icon: <MonitorIcon className=\"h-4 w-4\" />,\r\n    text: \"Dynamic Programming in DSA\",\r\n  },\r\n  {\r\n    icon: <Terminal className=\"h-4 w-4\" />,\r\n    text: \"Eigenvalues and Eigenvectors\",\r\n  },\r\n  { icon: <FileUp className=\"h-4 w-4\" />, text: \"Fourier Transform in Maths\" },\r\n  { icon: <Figma className=\"h-4 w-4\" />, text: \"Convex Optimization in Maths\" },\r\n  { icon: <MonitorIcon className=\"h-4 w-4\" />, text: \"Graph Theory in DSA\" },\r\n  { icon: <Terminal className=\"h-4 w-4\" />, text: \"Quantum Mechanics Basics\" },\r\n  { icon: <FileUp className=\"h-4 w-4\" />, text: \"Neural Networks in ML\" },\r\n];\r\n\r\ninterface InputSectionProps {\r\n  isGenerating: boolean;\r\n  onGenerate: (prompt: string) => void;\r\n}\r\n\r\nexport default function InputSection({\r\n  isGenerating,\r\n  onGenerate,\r\n}: InputSectionProps) {\r\n  const [inputValue, setInputValue] = useState(\"\");\r\n  const [isEnhancing, setIsEnhancing] = useState(false);\r\n\r\n  const enhancePrompt = async (prompt: string) => {\r\n    if (!prompt.trim()) {\r\n      setInputValue(\"\");\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setIsEnhancing(true);\r\n      localStorage.setItem(\"currentPrompt\", prompt.trim());\r\n\r\n      const response = await fetch(\"/api/enhance\", {\r\n        method: \"POST\",\r\n        headers: { \"Content-Type\": \"application/json\" },\r\n        body: JSON.stringify({ prompt: prompt.trim() }),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`HTTP error! status: ${response.status}`);\r\n      }\r\n\r\n      const enhancedPrompt = await response.text();\r\n      setInputValue(enhancedPrompt);\r\n    } catch (error) {\r\n      console.error(\"Error enhancing prompt:\", error);\r\n    } finally {\r\n      setIsEnhancing(false);\r\n    }\r\n  };\r\n\r\n  const handleGenerateClick = () => {\r\n    if (!inputValue.trim()) return;\r\n    // Store the full prompt value, not truncated\r\n    localStorage.setItem(\"currentPrompt\", inputValue.trim());\r\n    onGenerate(inputValue.trim());\r\n  };\r\n\r\n  return (\r\n    <AnimatePresence>\r\n      {!isGenerating && (\r\n        <motion.div\r\n          key=\"idle-ui\"\r\n          className=\"flex flex-grow flex-col h-full my-20 w-full items-center justify-center relative\"\r\n          initial={{ opacity: 0 }}\r\n          animate={{ opacity: 1 }}\r\n          exit={{ opacity: 0 }}\r\n          transition={{ duration: 0.4 }}\r\n        >\r\n          <div className=\"flex w-1/2 h-24 rounded-full bg-primary/20 blur-3xl absolute -top-10 left-1/2 -translate-x-1/2 text-foreground overflow-hidden\" />\r\n          <div className=\"mx-4 flex flex-col items-center\">\r\n            <div className=\"mb-12 text-center\">\r\n              <h1 className=\"mb-6 text-5xl md:text-6xl font-medium tracking-tight text-transparent bg-clip-text bg-gradient-to-br from-foreground to-muted/70 via-foreground/80\">\r\n                What do you want to learn?\r\n              </h1>\r\n              <p className=\"text-lg text-muted-foreground max-w-md mx-auto\">\r\n                Create animated explanations for any complex topic in minutes.\r\n                For both{\" \"}\r\n                <span className=\"font-medium text-foreground\">students</span>{\" \"}\r\n                and{\" \"}\r\n                <span className=\"font-medium text-foreground\">teachers</span> .\r\n              </p>\r\n            </div>\r\n\r\n            <div className=\"mx-auto mb-6 w-full max-w-xl\">\r\n              <div className=\"shadow-xl dark:shadow-primary/20 dark:shadow-2xl relative rounded-lg\">\r\n                <div className=\"flex flex-col rounded-lg border bg-gradient-to-b from-secondary/40 to-background p-3 pb-6 relative overflow-hidden\">\r\n                  <div className=\"absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent to-transparent via-primary pointer-events-none select-none\"></div>\r\n                  <div className=\"absolute bottom-0 left-0 w-full h-3 bg-gradient-to-r from-transparent to-transparent via-primary pointer-events-none select-none blur-2xl\"></div>\r\n                  <textarea\r\n                    placeholder=\"Explain bayes theorem in machine learning\"\r\n                    className=\"h-32 w-full outline-none resize-none text-sm\"\r\n                    value={inputValue}\r\n                    onChange={(e) => setInputValue(e.target.value)}\r\n                  />\r\n                  <div className=\"mt-auto flex gap-2 absolute bottom-2 right-2 \">\r\n                    <Button\r\n                      variant=\"ghost\"\r\n                      size=\"sm\"\r\n                      className={cn(\r\n                        \"backdrop-blur-lg shadow\",\r\n                        isEnhancing ? \"animate-pulse\" : null\r\n                      )}\r\n                      disabled={\r\n                        !inputValue.trim() ||\r\n                        isEnhancing ||\r\n                        inputValue.length < 6 ||\r\n                        inputValue.length > 300\r\n                      }\r\n                      onClick={() => {\r\n                        if (!inputValue.trim()) return;\r\n                        enhancePrompt(inputValue.trim());\r\n                      }}\r\n                    >\r\n                      {isEnhancing ? (\r\n                        <Loader className=\"animate-spin size-4\" />\r\n                      ) : (\r\n                        <Sparkles className=\"size-4\" />\r\n                      )}\r\n                      {isEnhancing ? \"Enhancing...\" : \"Enhance\"}\r\n                    </Button>\r\n                    <Button\r\n                      size=\"sm\"\r\n                      onClick={handleGenerateClick}\r\n                      disabled={!inputValue.trim() || isEnhancing}\r\n                    >\r\n                      Generate\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"mx-auto mt-16 flex w-full max-w-6xl flex-wrap justify-center gap-2\">\r\n              {EXAMPLE_ACTIONS.map((action, index) => (\r\n                <Button\r\n                  key={index}\r\n                  size=\"sm\"\r\n                  variant=\"outline\"\r\n                  className=\"rounded-full px-4 py-0.5 text-xs\"\r\n                  onClick={() => setInputValue(action.text)}\r\n                >\r\n                  {action.icon}\r\n                  <span>{action.text}</span>\r\n                </Button>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </motion.div>\r\n      )}\r\n    </AnimatePresence>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;;AAeA,MAAM,kBAAkB;IACtB;QAAE,oBAAM,8OAAC,oMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QAAc,MAAM;IAAyB;IACtE;QAAE,oBAAM,8OAAC,0MAAA,CAAA,SAAM;YAAC,WAAU;;;;;;QAAc,MAAM;IAA6B;IAC3E;QACE,oBAAM,8OAAC,wMAAA,CAAA,UAAW;YAAC,WAAU;;;;;;QAC7B,MAAM;IACR;IACA;QAAE,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QAAc,MAAM;IAAsB;IACtE;QAAE,oBAAM,8OAAC,0MAAA,CAAA,SAAM;YAAC,WAAU;;;;;;QAAc,MAAM;IAAsB;IACpE;QAAE,oBAAM,8OAAC,oMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QAAc,MAAM;IAA+B;IAC5E;QACE,oBAAM,8OAAC,0MAAA,CAAA,SAAM;YAAC,WAAU;;;;;;QACxB,MAAM;IACR;IACA;QACE,oBAAM,8OAAC,wMAAA,CAAA,UAAW;YAAC,WAAU;;;;;;QAC7B,MAAM;IACR;IACA;QACE,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QAC1B,MAAM;IACR;IACA;QAAE,oBAAM,8OAAC,0MAAA,CAAA,SAAM;YAAC,WAAU;;;;;;QAAc,MAAM;IAA6B;IAC3E;QAAE,oBAAM,8OAAC,oMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QAAc,MAAM;IAA+B;IAC5E;QAAE,oBAAM,8OAAC,wMAAA,CAAA,UAAW;YAAC,WAAU;;;;;;QAAc,MAAM;IAAsB;IACzE;QAAE,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QAAc,MAAM;IAA2B;IAC3E;QAAE,oBAAM,8OAAC,0MAAA,CAAA,SAAM;YAAC,WAAU;;;;;;QAAc,MAAM;IAAwB;CACvE;AAOc,SAAS,aAAa,EACnC,YAAY,EACZ,UAAU,EACQ;IAClB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,gBAAgB,OAAO;QAC3B,IAAI,CAAC,OAAO,IAAI,IAAI;YAClB,cAAc;YACd;QACF;QAEA,IAAI;YACF,eAAe;YACf,aAAa,OAAO,CAAC,iBAAiB,OAAO,IAAI;YAEjD,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE,QAAQ,OAAO,IAAI;gBAAG;YAC/C;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,iBAAiB,MAAM,SAAS,IAAI;YAC1C,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,WAAW,IAAI,IAAI;QACxB,6CAA6C;QAC7C,aAAa,OAAO,CAAC,iBAAiB,WAAW,IAAI;QACrD,WAAW,WAAW,IAAI;IAC5B;IAEA,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,CAAC,8BACA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YAET,WAAU;YACV,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;YACnB,YAAY;gBAAE,UAAU;YAAI;;8BAE5B,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqJ;;;;;;8CAGnK,8OAAC;oCAAE,WAAU;;wCAAiD;wCAEnD;sDACT,8OAAC;4CAAK,WAAU;sDAA8B;;;;;;wCAAgB;wCAAI;wCAC9D;sDACJ,8OAAC;4CAAK,WAAU;sDAA8B;;;;;;wCAAe;;;;;;;;;;;;;sCAIjE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CACC,aAAY;4CACZ,WAAU;4CACV,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;sDAE/C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2BACA,cAAc,kBAAkB;oDAElC,UACE,CAAC,WAAW,IAAI,MAChB,eACA,WAAW,MAAM,GAAG,KACpB,WAAW,MAAM,GAAG;oDAEtB,SAAS;wDACP,IAAI,CAAC,WAAW,IAAI,IAAI;wDACxB,cAAc,WAAW,IAAI;oDAC/B;;wDAEC,4BACC,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;iFAElB,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAErB,cAAc,iBAAiB;;;;;;;8DAElC,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAS;oDACT,UAAU,CAAC,WAAW,IAAI,MAAM;8DACjC;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQT,8OAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC,QAAQ,sBAC5B,8OAAC,kIAAA,CAAA,SAAM;oCAEL,MAAK;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,cAAc,OAAO,IAAI;;wCAEvC,OAAO,IAAI;sDACZ,8OAAC;sDAAM,OAAO,IAAI;;;;;;;mCAPb;;;;;;;;;;;;;;;;;WA1ET;;;;;;;;;;AA0Fd", "debugId": null}}, {"offset": {"line": 429, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Dialog({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\r\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\r\n}\r\n\r\nfunction DialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\r\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\r\n}\r\n\r\nfunction DialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\r\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\r\n}\r\n\r\nfunction DialogClose({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\r\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\r\n}\r\n\r\nfunction DialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\r\n  return (\r\n    <DialogPrimitive.Overlay\r\n      data-slot=\"dialog-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogContent({\r\n  className,\r\n  children,\r\n  showCloseButton = true,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\r\n  showCloseButton?: boolean\r\n}) {\r\n  return (\r\n    <DialogPortal data-slot=\"dialog-portal\">\r\n      <DialogOverlay />\r\n      <DialogPrimitive.Content\r\n        data-slot=\"dialog-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        {showCloseButton && (\r\n          <DialogPrimitive.Close\r\n            data-slot=\"dialog-close\"\r\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\r\n          >\r\n            <XIcon />\r\n            <span className=\"sr-only\">Close</span>\r\n          </DialogPrimitive.Close>\r\n        )}\r\n      </DialogPrimitive.Content>\r\n    </DialogPortal>\r\n  )\r\n}\r\n\r\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-header\"\r\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-footer\"\r\n      className={cn(\r\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\r\n  return (\r\n    <DialogPrimitive.Title\r\n      data-slot=\"dialog-title\"\r\n      className={cn(\"text-lg leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\r\n  return (\r\n    <DialogPrimitive.Description\r\n      data-slot=\"dialog-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Dialog,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogOverlay,\r\n  DialogPortal,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,8OAAC,kKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 603, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/components/mvpblocks/VideoConfirmationDialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { motion } from \"framer-motion\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from \"@/components/ui/dialog\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { CheckCircle2, Clock, Video, ArrowRight, ExternalLink } from \"lucide-react\";\r\nimport { useGeneration } from \"@/contexts/GenerationContext\";\r\n\r\ninterface VideoConfirmationDialogProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  onContinueWorking?: () => void;\r\n  videoJobId: string | null;\r\n  queuePosition?: number | null;\r\n}\r\n\r\nexport default function VideoConfirmationDialog({\r\n  isOpen,\r\n  onClose,\r\n  onContinueWorking,\r\n  videoJobId,\r\n  queuePosition,\r\n}: VideoConfirmationDialogProps) {\r\n  const { state } = useGeneration();\r\n\r\n  const handleGoToDashboard = () => {\r\n    onClose();\r\n    window.location.href = \"/dashboard\";\r\n  };\r\n\r\n  const handleGoToLibrary = () => {\r\n    onClose();\r\n    window.location.href = \"/library\";\r\n  };\r\n\r\n  const handleContinueWorking = () => {\r\n    // Use custom handler if provided, otherwise just close\r\n    if (onContinueWorking) {\r\n      onContinueWorking();\r\n    } else {\r\n      onClose();\r\n    }\r\n  };\r\n\r\n  // Check if video is completed\r\n  const isCompleted = state.isCompleted && state.completedVideoUrl;\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={onClose}>\r\n      <DialogContent>\r\n        <DialogHeader className=\"text-center space-y-4\">\r\n          <div className=\"mx-auto w-16 h-16 bg-gradient-to-br from-green-400/20 to-emerald-500/10 rounded-full flex items-center justify-center\">\r\n            <motion.div\r\n              initial={{ scale: 0 }}\r\n              animate={{ scale: 1 }}\r\n              transition={{ delay: 0.2, type: \"spring\", stiffness: 200 }}\r\n            >\r\n              <CheckCircle2 className=\"w-8 h-8 text-green-500\" />\r\n            </motion.div>\r\n          </div>\r\n\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 10 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ delay: 0.3 }}\r\n          >\r\n            <DialogTitle>\r\n              {isCompleted ? \"Video Generation Complete!\" : \"Video Generation Started!\"}\r\n            </DialogTitle>\r\n          </motion.div>\r\n        </DialogHeader>\r\n\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 10 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.4 }}\r\n          className=\"space-y-4\"\r\n        >\r\n          <DialogDescription className=\"text-muted-foreground leading-relaxed\">\r\n            Your animated educational video is now being generated. This process\r\n            typically takes 3-5 minutes.\r\n          </DialogDescription>\r\n\r\n          {videoJobId && (\r\n            <div className=\"bg-gradient-to-r from-primary/5 to-secondary/5 rounded-lg p-4 border border-primary/10\">\r\n              <div className=\"flex items-center space-x-3\">\r\n                <div className=\"w-2 h-2 rounded-full bg-gradient-to-r from-primary to-secondary animate-pulse\"></div>\r\n                <div className=\"flex-1\">\r\n                  <div className=\"text-sm font-medium text-foreground\">\r\n                    Job ID: {videoJobId}\r\n                  </div>\r\n                  {queuePosition && (\r\n                    <div className=\"text-xs text-muted-foreground\">\r\n                      Queue Position: #{queuePosition}\r\n                    </div>\r\n                  )}\r\n                  <div className=\"text-xs text-muted-foreground\">\r\n                    Track your video generation progress\r\n                  </div>\r\n                </div>\r\n                <Video className=\"w-5 h-5 text-primary\" />\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          <div className=\"bg-gradient-to-r from-blue-500/5 to-cyan-500/5 rounded-lg p-4 border border-blue-500/10\">\r\n            <div className=\"flex items-start space-x-3\">\r\n              <Clock className=\"w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0\" />\r\n              <div className=\"space-y-2\">\r\n                <p className=\"text-sm font-medium text-foreground\">\r\n                  What happens next?\r\n                </p>\r\n                <ul className=\"text-xs text-muted-foreground space-y-1\">\r\n                  <li>• Your video will be processed in the background</li>\r\n                  <li>• You'll receive a notification when it's ready</li>\r\n                  <li>• The video will be available in your dashboard</li>\r\n                  <li>• You can download or share it directly</li>\r\n                </ul>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </motion.div>\r\n\r\n        <DialogFooter className=\"flex-col sm:flex-row gap-2\">\r\n          {isCompleted ? (\r\n            <>\r\n              <Button\r\n                variant=\"outline\"\r\n                onClick={handleContinueWorking}\r\n                className=\"w-full sm:w-auto\"\r\n              >\r\n                Generate Another\r\n              </Button>\r\n              <Button onClick={handleGoToLibrary} className=\"bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600\">\r\n                <span>View in Library</span>\r\n                <ExternalLink className=\"w-4 h-4 ml-2\" />\r\n              </Button>\r\n            </>\r\n          ) : (\r\n            <>\r\n              <Button\r\n                variant=\"outline\"\r\n                onClick={handleContinueWorking}\r\n                className=\"w-full sm:w-auto\"\r\n              >\r\n                Continue Working\r\n              </Button>\r\n              <Button onClick={handleGoToDashboard}>\r\n                <span>Go to Dashboard</span>\r\n                <ArrowRight className=\"w-4 h-4 ml-2\" />\r\n              </Button>\r\n            </>\r\n          )}\r\n        </DialogFooter>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAbA;;;;;;;AAuBe,SAAS,wBAAwB,EAC9C,MAAM,EACN,OAAO,EACP,iBAAiB,EACjB,UAAU,EACV,aAAa,EACgB;IAC7B,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,gBAAa,AAAD;IAE9B,MAAM,sBAAsB;QAC1B;QACA,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,MAAM,oBAAoB;QACxB;QACA,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,MAAM,wBAAwB;QAC5B,uDAAuD;QACvD,IAAI,mBAAmB;YACrB;QACF,OAAO;YACL;QACF;IACF;IAEA,8BAA8B;IAC9B,MAAM,cAAc,MAAM,WAAW,IAAI,MAAM,iBAAiB;IAEhE,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;;8BACZ,8OAAC,kIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,OAAO;gCAAE;gCACpB,SAAS;oCAAE,OAAO;gCAAE;gCACpB,YAAY;oCAAE,OAAO;oCAAK,MAAM;oCAAU,WAAW;gCAAI;0CAEzD,cAAA,8OAAC,qNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;;;;;sCAI5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;4BAAI;sCAEzB,cAAA,8OAAC,kIAAA,CAAA,cAAW;0CACT,cAAc,+BAA+B;;;;;;;;;;;;;;;;;8BAKpD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,OAAO;oBAAI;oBACzB,WAAU;;sCAEV,8OAAC,kIAAA,CAAA,oBAAiB;4BAAC,WAAU;sCAAwC;;;;;;wBAKpE,4BACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;oDAAsC;oDAC1C;;;;;;;4CAEV,+BACC,8OAAC;gDAAI,WAAU;;oDAAgC;oDAC3B;;;;;;;0DAGtB,8OAAC;gDAAI,WAAU;0DAAgC;;;;;;;;;;;;kDAIjD,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKvB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAsC;;;;;;0DAGnD,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOd,8OAAC,kIAAA,CAAA,eAAY;oBAAC,WAAU;8BACrB,4BACC;;0CACE,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS;gCACT,WAAU;0CACX;;;;;;0CAGD,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAS;gCAAmB,WAAU;;kDAC5C,8OAAC;kDAAK;;;;;;kDACN,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;;qDAI5B;;0CACE,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS;gCACT,WAAU;0CACX;;;;;;0CAGD,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAS;;kDACf,8OAAC;kDAAK;;;;;;kDACN,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtC", "debugId": null}}, {"offset": {"line": 985, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/hooks/useAuthUser.ts"], "sourcesContent": ["// hooks/useAuthUser.ts\r\nimport { useEffect, useState } from \"react\";\r\n\r\nexport function useAuthUser() {\r\n  const [user, setUser] = useState<any>(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [unauthenticated, setUnauthenticated] = useState(false);\r\n\r\n  useEffect(() => {\r\n    async function fetchUser() {\r\n      try {\r\n        const res = await fetch(\"/api/user\");\r\n        if (res.status === 401) {\r\n          setUnauthenticated(true);\r\n          setUser(null);\r\n        } else {\r\n          const data = await res.json();\r\n          setUser(data.user);\r\n        }\r\n      } catch (err) {\r\n        console.error(\"Failed to fetch user\", err);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    }\r\n\r\n    fetchUser();\r\n  }, []);\r\n\r\n  return { user, loading, unauthenticated };\r\n}\r\n"], "names": [], "mappings": "AAAA,uBAAuB;;;;AACvB;;AAEO,SAAS;IACd,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACtC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,eAAe;YACb,IAAI;gBACF,MAAM,MAAM,MAAM,MAAM;gBACxB,IAAI,IAAI,MAAM,KAAK,KAAK;oBACtB,mBAAmB;oBACnB,QAAQ;gBACV,OAAO;oBACL,MAAM,OAAO,MAAM,IAAI,IAAI;oBAC3B,QAAQ,KAAK,IAAI;gBACnB;YACF,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,wBAAwB;YACxC,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,OAAO;QAAE;QAAM;QAAS;IAAgB;AAC1C", "debugId": null}}, {"offset": {"line": 1026, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/components/mvpblocks/bolt.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, useRef, useCallback } from \"react\";\r\nimport InputSection from \"./InputSection\";\r\nimport VideoConfirmationDialog from \"./VideoConfirmationDialog\";\r\nimport { type Entries } from \"@prisma/client\";\r\nimport { useAuthUser } from \"@/hooks/useAuthUser\";\r\nimport dynamic from \"next/dynamic\";\r\n\r\nconst ContentDisplayPanel = dynamic(() => import(\"./ContentDisplayPanel\"), {\r\n  ssr: false,\r\n});\r\n\r\nconst TaskProgressSidebar = dynamic(() => import(\"./TaskProgressSidebar\"), {\r\n  ssr: false,\r\n});\r\n\r\ninterface ScriptItem {\r\n  title: string;\r\n  description: string;\r\n  code?: string;\r\n}\r\n\r\ninterface Task {\r\n  id: string;\r\n  name: string;\r\n  status: \"pending\" | \"in-progress\" | \"completed\" | \"failed\";\r\n}\r\n\r\nconst BACKEND_URL =\r\n  process.env.NEXT_PUBLIC_BACKEND_URL || \"http://localhost:8000\";\r\n\r\nexport default function Bolt() {\r\n  const { user } = useAuthUser();\r\n  const abortControllerRef = useRef<AbortController | null>(null);\r\n  const [isGenerating, setIsGenerating] = useState(false);\r\n  const [showGenerationUI, setShowGenerationUI] = useState(false);\r\n  const [tasks, setTasks] = useState<Task[]>([]);\r\n  const [currentScripts, setCurrentScripts] = useState<ScriptItem[]>([]);\r\n  const [showConfirmationDialog, setShowConfirmationDialog] = useState(false);\r\n  const [videoJobId, setVideoJobId] = useState<string | null>(null);\r\n  const [queuePosition, setQueuePosition] = useState<number | null>(null);\r\n  const [, setQuizId] = useState<string | null>(null);\r\n  const [mainTheme, setMainTheme] = useState<string>(\"\");\r\n\r\n  useEffect(() => {\r\n    if (typeof window !== \"undefined\") {\r\n      const savedPrompt = localStorage.getItem(\"currentPrompt\") || \"\";\r\n      setMainTheme(savedPrompt);\r\n    }\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    return () => {\r\n      if (abortControllerRef.current) {\r\n        abortControllerRef.current.abort();\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  const generateScriptWithFetch = useCallback(async (prompt: string) => {\r\n    try {\r\n      setIsGenerating(true);\r\n\r\n      abortControllerRef.current = new AbortController();\r\n\r\n      const response = await fetch(\"/api/generate-script\", {\r\n        method: \"POST\",\r\n        headers: { \"Content-Type\": \"application/json\" },\r\n        body: JSON.stringify({ prompt }),\r\n        signal: abortControllerRef.current.signal,\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`HTTP error! status: ${response.status}`);\r\n      }\r\n\r\n      const generatedScripts: ScriptItem[] = await response.json();\r\n      setCurrentScripts(generatedScripts);\r\n\r\n      setTasks((prevTasks) =>\r\n        prevTasks.map((task) =>\r\n          task.id === \"1\"\r\n            ? { ...task, status: \"completed\" }\r\n            : task.id === \"2\"\r\n            ? { ...task, status: \"completed\" }\r\n            : task\r\n        )\r\n      );\r\n\r\n      // Wait for quiz generation and get the quizId\r\n      console.log(\"🧠 Starting quiz generation...\");\r\n      const generatedQuizId = await generateQuizWithFetch(\r\n        generatedScripts.map((script) => script.title),\r\n        generatedScripts.map((script) => script.description)\r\n      );\r\n\r\n      console.log(`✅ Quiz generation completed. Quiz ID: ${generatedQuizId}`);\r\n\r\n      // Pass the quizId to generateManimCodes\r\n      console.log(\"🎨 Starting manim code generation...\");\r\n      await generateManimCodes(generatedScripts, generatedQuizId, prompt);\r\n    } catch (error) {\r\n      if (error instanceof Error && error.name === \"AbortError\") {\r\n        console.log(\"Request was aborted\");\r\n        return;\r\n      }\r\n\r\n      setTasks((prevTasks) =>\r\n        prevTasks.map((task) =>\r\n          task.status === \"in-progress\"\r\n            ? {\r\n                ...task,\r\n                status: \"failed\",\r\n                name: `${task.name} (Error: ${\r\n                  error instanceof Error ? error.message : String(error)\r\n                })`,\r\n              }\r\n            : task\r\n        )\r\n      );\r\n    }\r\n  }, []);\r\n\r\n  const generateQuizWithFetch = async (\r\n    title: string[],\r\n    description: string[]\r\n  ): Promise<string | null> => {\r\n    try {\r\n      const combinedTitle = title.map((t) => t.trim()).join(\",\");\r\n      const combinedDescription = description.map((d) => d.trim()).join(\"\\n\");\r\n      const userId = user.id;\r\n\r\n      const response = await fetch(\"/api/ai-quiz\", {\r\n        method: \"POST\",\r\n        headers: { \"Content-Type\": \"application/json\" },\r\n        body: JSON.stringify({\r\n          title: combinedTitle,\r\n          content: combinedDescription,\r\n          userId: userId,\r\n        }),\r\n        signal: abortControllerRef.current?.signal,\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`HTTP error! status: ${response.status}`);\r\n      }\r\n\r\n      const quizData = (await response.json()) as Entries;\r\n      setQuizId(quizData.id);\r\n      return quizData.id; // Return the quizId\r\n    } catch (error) {\r\n      if (error instanceof Error && error.name === \"AbortError\") {\r\n        return null;\r\n      }\r\n\r\n      console.error(\"Error generating quiz:\", error);\r\n      return null;\r\n    }\r\n  };\r\n\r\n  const generateManimCodeWithFetch = useCallback(\r\n    async (\r\n      title: string,\r\n      description: string,\r\n      currentPrompt: string\r\n    ): Promise<string | null> => {\r\n      try {\r\n        console.log(`🎨 Generating manim code for: ${title}`);\r\n\r\n        const response = await fetch(\"/api/manim\", {\r\n          method: \"POST\",\r\n          headers: { \"Content-Type\": \"application/json\" },\r\n          body: JSON.stringify({\r\n            schema: { title, description },\r\n            mainTheme: currentPrompt,\r\n          }),\r\n          signal: abortControllerRef.current?.signal,\r\n        });\r\n\r\n        if (!response.ok) {\r\n          console.error(`❌ Manim API error for ${title}: ${response.status}`);\r\n          throw new Error(`HTTP error! status: ${response.status}`);\r\n        }\r\n\r\n        const data = await response.json();\r\n        const code = data.code;\r\n\r\n        console.log(`✅ Manim code generated for ${title}: ${code ? 'Success' : 'No code returned'}`);\r\n\r\n        if (!code) {\r\n          console.warn(`⚠️ No code returned for ${title}`);\r\n          return null;\r\n        }\r\n\r\n        setCurrentScripts((prev) =>\r\n          prev.map((script) =>\r\n            script.title === title ? { ...script, code: code } : script\r\n          )\r\n        );\r\n\r\n        setTasks((prevTasks) =>\r\n          prevTasks.map((task) =>\r\n            task.id === `manim-${title}`\r\n              ? { ...task, status: \"completed\" }\r\n              : task\r\n          )\r\n        );\r\n\r\n        return code;\r\n      } catch (error) {\r\n        if (error instanceof Error && error.name === \"AbortError\") {\r\n          return null;\r\n        }\r\n\r\n        setTasks((prevTasks) =>\r\n          prevTasks.map((task) =>\r\n            task.id === `manim-${title}`\r\n              ? {\r\n                  ...task,\r\n                  status: \"failed\",\r\n                  name: `${task.name} (Error: ${\r\n                    error instanceof Error ? error.message : String(error)\r\n                  })`,\r\n                }\r\n              : task\r\n          )\r\n        );\r\n        return null;\r\n      }\r\n    },\r\n    [] // Remove mainTheme dependency since we're passing it as parameter\r\n  );\r\n\r\n  const renderVideoWithBackend = useCallback(\r\n    async (scripts: ScriptItem[], quizId: string) => {\r\n      try {\r\n        console.log(\"🎬 Starting renderVideoWithBackend function\");\r\n        console.log(`📝 Received ${scripts.length} scripts`);\r\n        console.log(`🆔 Quiz ID: ${quizId}`);\r\n\r\n        const manimCodes = scripts\r\n          .filter((script) => script.code)\r\n          .map((script) => script.code!);\r\n\r\n        console.log(`✅ Filtered to ${manimCodes.length} scripts with valid code`);\r\n\r\n        if (manimCodes.length === 0) {\r\n          console.error(\"❌ No valid scripts to render\");\r\n          throw new Error(\"No valid scripts to render\");\r\n        }\r\n\r\n        if (!quizId) {\r\n          console.error(\"❌ Quiz ID not available\");\r\n          throw new Error(\"Quiz ID not available\");\r\n        }\r\n\r\n        // Prepare the batch render payload with the new format\r\n        const scriptsData = scripts\r\n          .filter((script) => script.code)\r\n          .map((script) => ({\r\n            manim_code: script.code!,\r\n            description: script.description,\r\n          }));\r\n\r\n        const renderPayload = {\r\n          topicName: mainTheme || \"Generated Topic\",\r\n          entryId: quizId,\r\n          scripts: scriptsData,\r\n          priority: 0,\r\n        };\r\n\r\n        // Mark entry as generating in database\r\n        try {\r\n          await fetch(\"/api/start-generation\", {\r\n            method: \"POST\",\r\n            headers: { \"Content-Type\": \"application/json\" },\r\n            body: JSON.stringify({ entryId: quizId }),\r\n          });\r\n          console.log(\"✅ Entry marked as generating in database\");\r\n        } catch (dbError) {\r\n          console.error(\"⚠️ Failed to mark entry as generating:\", dbError);\r\n          // Continue with video generation even if DB update fails\r\n        }\r\n\r\n        console.log(\"Sending batch render request:\", renderPayload);\r\n\r\n        const response = await fetch(`${BACKEND_URL}/batch_render`, {\r\n          method: \"POST\",\r\n          headers: { \"Content-Type\": \"application/json\" },\r\n          body: JSON.stringify(renderPayload),\r\n          signal: abortControllerRef.current?.signal,\r\n        });\r\n\r\n        if (!response.ok) {\r\n          const errorText = await response.text();\r\n          throw new Error(`Backend error: ${response.status} - ${errorText}`);\r\n        }\r\n\r\n        const result = await response.json();\r\n        console.log(\"Batch render response:\", result);\r\n\r\n        setVideoJobId(result.job_id);\r\n        setQueuePosition(result.queue_position);\r\n\r\n        console.log(`🎯 Video job queued successfully!`);\r\n        console.log(`🆔 Job ID: ${result.job_id}`);\r\n        console.log(`📍 Queue Position: ${result.queue_position}`);\r\n\r\n        setTasks((prevTasks) => [\r\n          ...prevTasks,\r\n          {\r\n            id: \"4\",\r\n            name: \"Rendering Video\",\r\n            status: \"completed\",\r\n          },\r\n        ]);\r\n\r\n        // Show confirmation dialog after a short delay\r\n        setTimeout(() => {\r\n          setIsGenerating(false);\r\n          console.log(\"🎉 Showing confirmation dialog\");\r\n          setShowConfirmationDialog(true);\r\n        }, 500);\r\n      } catch (error) {\r\n        if (error instanceof Error && error.name === \"AbortError\") {\r\n          return;\r\n        }\r\n\r\n        console.error(\"Error rendering video:\", error);\r\n        setTasks((prevTasks) => [\r\n          ...prevTasks,\r\n          {\r\n            id: \"4\",\r\n            name: `Video Rendering Failed: ${\r\n              error instanceof Error ? error.message : String(error)\r\n            }`,\r\n            status: \"failed\",\r\n          },\r\n        ]);\r\n      }\r\n    },\r\n    []\r\n  );\r\n\r\n  const generateManimCodes = useCallback(\r\n    async (\r\n      scripts: ScriptItem[],\r\n      quizId: string | null,\r\n      currentPrompt: string\r\n    ) => {\r\n      console.log(\"🎨 generateManimCodes function called\");\r\n      console.log(`📝 Scripts to process: ${scripts.length}`);\r\n      console.log(`🆔 Quiz ID: ${quizId}`);\r\n      console.log(`💭 Current prompt: ${currentPrompt}`);\r\n\r\n      const manimTasks: Task[] = scripts.map((script) => ({\r\n        id: `manim-${script.title}`,\r\n        name: `Generating: ${script.title}`,\r\n        status: \"pending\",\r\n      }));\r\n\r\n      setTasks((prevTasks) => [\r\n        ...prevTasks.filter((t) => t.id !== \"3\"),\r\n        ...manimTasks,\r\n        { id: \"3\", name: \"Generating Animations\", status: \"in-progress\" },\r\n      ]);\r\n\r\n      const scriptsWithCode: ScriptItem[] = [];\r\n\r\n      for (const scriptItem of scripts) {\r\n        setTasks((prevTasks) =>\r\n          prevTasks.map((task) =>\r\n            task.id === `manim-${scriptItem.title}`\r\n              ? { ...task, status: \"in-progress\" }\r\n              : task\r\n          )\r\n        );\r\n\r\n        const code = await generateManimCodeWithFetch(\r\n          scriptItem.title,\r\n          scriptItem.description,\r\n          currentPrompt\r\n        );\r\n\r\n        if (code) {\r\n          console.log(`✅ Adding script \"${scriptItem.title}\" to scriptsWithCode`);\r\n          scriptsWithCode.push({ ...scriptItem, code });\r\n        } else {\r\n          console.warn(`⚠️ No code generated for script \"${scriptItem.title}\", skipping`);\r\n        }\r\n      }\r\n\r\n      setTasks((prevTasks) =>\r\n        prevTasks.map((task) =>\r\n          task.id === \"3\" ? { ...task, status: \"completed\" } : task\r\n        )\r\n      );\r\n\r\n      console.log(\"🎬 Manim code generation completed!\");\r\n      console.log(`📊 Scripts with code: ${scriptsWithCode.length}`);\r\n      console.log(`🆔 Quiz ID: ${quizId}`);\r\n      console.log(\"📝 Scripts with code:\", scriptsWithCode.map(s => ({ title: s.title, hasCode: !!s.code })));\r\n\r\n      // Trigger video rendering after animation generation is complete\r\n      if (scriptsWithCode.length > 0 && quizId) {\r\n        console.log(\"✅ All conditions met, starting video rendering...\");\r\n\r\n        // Show confirmation dialog first\r\n        setShowConfirmationDialog(true);\r\n\r\n        // Start video rendering\r\n        try {\r\n          await renderVideoWithBackend(scriptsWithCode, quizId);\r\n        } catch (error) {\r\n          console.error(\"❌ Video rendering failed:\", error);\r\n          setTasks((prevTasks) =>\r\n            prevTasks.map((task) =>\r\n              task.id === \"3\"\r\n                ? { ...task, status: \"failed\", name: \"Animation Generation (Render Failed)\" }\r\n                : task\r\n            )\r\n          );\r\n        }\r\n      } else {\r\n        console.warn(\"❌ Cannot start video rendering:\");\r\n        console.warn(`  - Scripts with code: ${scriptsWithCode.length}`);\r\n        console.warn(`  - Quiz ID: ${quizId}`);\r\n        console.warn(\"  - This means either no manim codes were generated or quiz creation failed\");\r\n      }\r\n    },\r\n    [generateManimCodeWithFetch, renderVideoWithBackend]\r\n  );\r\n\r\n  const handleGenerateClick = useCallback(\r\n    async (prompt: string) => {\r\n      // Check if user already has an active generation\r\n      try {\r\n        const response = await fetch(\"/api/check-generation-status\");\r\n        if (response.ok) {\r\n          const { hasActiveGeneration, activeEntry } = await response.json();\r\n\r\n          if (hasActiveGeneration) {\r\n            alert(`You already have a video generation in progress for: \"${activeEntry.prompt}\". Please wait for it to complete before starting a new one.`);\r\n            return;\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Failed to check generation status:\", error);\r\n        // Continue with generation if check fails\r\n      }\r\n\r\n      setIsGenerating(true);\r\n      setShowGenerationUI(true);\r\n      setCurrentScripts([]);\r\n      setQuizId(null);\r\n\r\n      if (typeof window !== \"undefined\") {\r\n        localStorage.setItem(\"currentPrompt\", prompt);\r\n        setMainTheme(prompt);\r\n      }\r\n\r\n      const initialTasks: Task[] = [\r\n        { id: \"1\", name: \"Analyzing Input\", status: \"in-progress\" },\r\n        { id: \"2\", name: \"Generating Script\", status: \"pending\" },\r\n        { id: \"3\", name: \"Generating Animations\", status: \"pending\" },\r\n      ];\r\n      setTasks(initialTasks);\r\n\r\n      try {\r\n        setTasks((prevTasks) =>\r\n          prevTasks.map((task) =>\r\n            task.id === \"2\" ? { ...task, status: \"in-progress\" } : task\r\n          )\r\n        );\r\n        await generateScriptWithFetch(prompt);\r\n      } catch (error) {\r\n        if (error instanceof Error && error.name === \"AbortError\") {\r\n          console.log(\"Generation was aborted\");\r\n          return;\r\n        }\r\n\r\n        setTasks((prevTasks) =>\r\n          prevTasks.map((task) =>\r\n            task.status === \"in-progress\"\r\n              ? {\r\n                  ...task,\r\n                  status: \"failed\",\r\n                  name: `${task.name} (Error: ${\r\n                    error instanceof Error ? error.message : String(error)\r\n                  })`,\r\n                }\r\n              : task\r\n          )\r\n        );\r\n      }\r\n    },\r\n    [generateScriptWithFetch]\r\n  );\r\n\r\n  const handleDialogClose = useCallback(() => {\r\n    setShowConfirmationDialog(false);\r\n  }, []);\r\n\r\n  const handleResetToInput = useCallback(() => {\r\n    setShowConfirmationDialog(false);\r\n    setTimeout(() => {\r\n      setIsGenerating(false);\r\n      setShowGenerationUI(false);\r\n      setCurrentScripts([]);\r\n      setTasks([]);\r\n      setVideoJobId(null);\r\n      setQuizId(null);\r\n    }, 300);\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"h-full flex flex-col\">\r\n      <InputSection\r\n        key={\"input-section\"}\r\n        isGenerating={isGenerating}\r\n        onGenerate={handleGenerateClick}\r\n      />\r\n      {showGenerationUI && (\r\n        <div className=\"flex-1 w-full h-0 overflow-hidden\">\r\n          <div className=\"h-full w-full grid grid-cols-1 lg:grid-cols-7 gap-0\">\r\n            <TaskProgressSidebar isGenerating={isGenerating} tasks={tasks} />\r\n\r\n            <ContentDisplayPanel\r\n              isGenerating={isGenerating}\r\n              currentScripts={currentScripts}\r\n            />\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      <VideoConfirmationDialog\r\n        key={\"video-confirmation-dialog\"}\r\n        isOpen={showConfirmationDialog}\r\n        onClose={handleDialogClose}\r\n        onContinueWorking={handleResetToInput}\r\n        videoJobId={videoJobId}\r\n        queuePosition={queuePosition}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;;;AAPA;;;;;;;AASA,MAAM,sBAAsB,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD;;;;;;IAChC,KAAK;;AAGP,MAAM,sBAAsB,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD;;;;;;IAChC,KAAK;;AAeP,MAAM,cACJ,8FAAuC;AAE1B,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA0B;IAC1D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACrE,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,GAAG,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uCAAmC;;QAGnC;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL,IAAI,mBAAmB,OAAO,EAAE;gBAC9B,mBAAmB,OAAO,CAAC,KAAK;YAClC;QACF;IACF,GAAG,EAAE;IAEL,MAAM,0BAA0B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACjD,IAAI;YACF,gBAAgB;YAEhB,mBAAmB,OAAO,GAAG,IAAI;YAEjC,MAAM,WAAW,MAAM,MAAM,wBAAwB;gBACnD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAO;gBAC9B,QAAQ,mBAAmB,OAAO,CAAC,MAAM;YAC3C;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,mBAAiC,MAAM,SAAS,IAAI;YAC1D,kBAAkB;YAElB,SAAS,CAAC,YACR,UAAU,GAAG,CAAC,CAAC,OACb,KAAK,EAAE,KAAK,MACR;wBAAE,GAAG,IAAI;wBAAE,QAAQ;oBAAY,IAC/B,KAAK,EAAE,KAAK,MACZ;wBAAE,GAAG,IAAI;wBAAE,QAAQ;oBAAY,IAC/B;YAIR,8CAA8C;YAC9C,QAAQ,GAAG,CAAC;YACZ,MAAM,kBAAkB,MAAM,sBAC5B,iBAAiB,GAAG,CAAC,CAAC,SAAW,OAAO,KAAK,GAC7C,iBAAiB,GAAG,CAAC,CAAC,SAAW,OAAO,WAAW;YAGrD,QAAQ,GAAG,CAAC,CAAC,sCAAsC,EAAE,iBAAiB;YAEtE,wCAAwC;YACxC,QAAQ,GAAG,CAAC;YACZ,MAAM,mBAAmB,kBAAkB,iBAAiB;QAC9D,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,SAAS,MAAM,IAAI,KAAK,cAAc;gBACzD,QAAQ,GAAG,CAAC;gBACZ;YACF;YAEA,SAAS,CAAC,YACR,UAAU,GAAG,CAAC,CAAC,OACb,KAAK,MAAM,KAAK,gBACZ;wBACE,GAAG,IAAI;wBACP,QAAQ;wBACR,MAAM,GAAG,KAAK,IAAI,CAAC,SAAS,EAC1B,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO,OACjD,CAAC,CAAC;oBACL,IACA;QAGV;IACF,GAAG,EAAE;IAEL,MAAM,wBAAwB,OAC5B,OACA;QAEA,IAAI;YACF,MAAM,gBAAgB,MAAM,GAAG,CAAC,CAAC,IAAM,EAAE,IAAI,IAAI,IAAI,CAAC;YACtD,MAAM,sBAAsB,YAAY,GAAG,CAAC,CAAC,IAAM,EAAE,IAAI,IAAI,IAAI,CAAC;YAClE,MAAM,SAAS,KAAK,EAAE;YAEtB,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,OAAO;oBACP,SAAS;oBACT,QAAQ;gBACV;gBACA,QAAQ,mBAAmB,OAAO,EAAE;YACtC;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,WAAY,MAAM,SAAS,IAAI;YACrC,UAAU,SAAS,EAAE;YACrB,OAAO,SAAS,EAAE,EAAE,oBAAoB;QAC1C,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,SAAS,MAAM,IAAI,KAAK,cAAc;gBACzD,OAAO;YACT;YAEA,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO;QACT;IACF;IAEA,MAAM,6BAA6B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC3C,OACE,OACA,aACA;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,8BAA8B,EAAE,OAAO;YAEpD,MAAM,WAAW,MAAM,MAAM,cAAc;gBACzC,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;wBAAE;wBAAO;oBAAY;oBAC7B,WAAW;gBACb;gBACA,QAAQ,mBAAmB,OAAO,EAAE;YACtC;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,MAAM,EAAE,EAAE,SAAS,MAAM,EAAE;gBAClE,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,MAAM,OAAO,KAAK,IAAI;YAEtB,QAAQ,GAAG,CAAC,CAAC,2BAA2B,EAAE,MAAM,EAAE,EAAE,OAAO,YAAY,oBAAoB;YAE3F,IAAI,CAAC,MAAM;gBACT,QAAQ,IAAI,CAAC,CAAC,wBAAwB,EAAE,OAAO;gBAC/C,OAAO;YACT;YAEA,kBAAkB,CAAC,OACjB,KAAK,GAAG,CAAC,CAAC,SACR,OAAO,KAAK,KAAK,QAAQ;wBAAE,GAAG,MAAM;wBAAE,MAAM;oBAAK,IAAI;YAIzD,SAAS,CAAC,YACR,UAAU,GAAG,CAAC,CAAC,OACb,KAAK,EAAE,KAAK,CAAC,MAAM,EAAE,OAAO,GACxB;wBAAE,GAAG,IAAI;wBAAE,QAAQ;oBAAY,IAC/B;YAIR,OAAO;QACT,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,SAAS,MAAM,IAAI,KAAK,cAAc;gBACzD,OAAO;YACT;YAEA,SAAS,CAAC,YACR,UAAU,GAAG,CAAC,CAAC,OACb,KAAK,EAAE,KAAK,CAAC,MAAM,EAAE,OAAO,GACxB;wBACE,GAAG,IAAI;wBACP,QAAQ;wBACR,MAAM,GAAG,KAAK,IAAI,CAAC,SAAS,EAC1B,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO,OACjD,CAAC,CAAC;oBACL,IACA;YAGR,OAAO;QACT;IACF,GACA,EAAE,CAAC,kEAAkE;;IAGvE,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACvC,OAAO,SAAuB;QAC5B,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,QAAQ,MAAM,CAAC,QAAQ,CAAC;YACnD,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,QAAQ;YAEnC,MAAM,aAAa,QAChB,MAAM,CAAC,CAAC,SAAW,OAAO,IAAI,EAC9B,GAAG,CAAC,CAAC,SAAW,OAAO,IAAI;YAE9B,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,WAAW,MAAM,CAAC,wBAAwB,CAAC;YAExE,IAAI,WAAW,MAAM,KAAK,GAAG;gBAC3B,QAAQ,KAAK,CAAC;gBACd,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,CAAC,QAAQ;gBACX,QAAQ,KAAK,CAAC;gBACd,MAAM,IAAI,MAAM;YAClB;YAEA,uDAAuD;YACvD,MAAM,cAAc,QACjB,MAAM,CAAC,CAAC,SAAW,OAAO,IAAI,EAC9B,GAAG,CAAC,CAAC,SAAW,CAAC;oBAChB,YAAY,OAAO,IAAI;oBACvB,aAAa,OAAO,WAAW;gBACjC,CAAC;YAEH,MAAM,gBAAgB;gBACpB,WAAW,aAAa;gBACxB,SAAS;gBACT,SAAS;gBACT,UAAU;YACZ;YAEA,uCAAuC;YACvC,IAAI;gBACF,MAAM,MAAM,yBAAyB;oBACnC,QAAQ;oBACR,SAAS;wBAAE,gBAAgB;oBAAmB;oBAC9C,MAAM,KAAK,SAAS,CAAC;wBAAE,SAAS;oBAAO;gBACzC;gBACA,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,SAAS;gBAChB,QAAQ,KAAK,CAAC,0CAA0C;YACxD,yDAAyD;YAC3D;YAEA,QAAQ,GAAG,CAAC,iCAAiC;YAE7C,MAAM,WAAW,MAAM,MAAM,GAAG,YAAY,aAAa,CAAC,EAAE;gBAC1D,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;gBACrB,QAAQ,mBAAmB,OAAO,EAAE;YACtC;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,CAAC,eAAe,EAAE,SAAS,MAAM,CAAC,GAAG,EAAE,WAAW;YACpE;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,QAAQ,GAAG,CAAC,0BAA0B;YAEtC,cAAc,OAAO,MAAM;YAC3B,iBAAiB,OAAO,cAAc;YAEtC,QAAQ,GAAG,CAAC,CAAC,iCAAiC,CAAC;YAC/C,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,OAAO,MAAM,EAAE;YACzC,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,OAAO,cAAc,EAAE;YAEzD,SAAS,CAAC,YAAc;uBACnB;oBACH;wBACE,IAAI;wBACJ,MAAM;wBACN,QAAQ;oBACV;iBACD;YAED,+CAA+C;YAC/C,WAAW;gBACT,gBAAgB;gBAChB,QAAQ,GAAG,CAAC;gBACZ,0BAA0B;YAC5B,GAAG;QACL,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,SAAS,MAAM,IAAI,KAAK,cAAc;gBACzD;YACF;YAEA,QAAQ,KAAK,CAAC,0BAA0B;YACxC,SAAS,CAAC,YAAc;uBACnB;oBACH;wBACE,IAAI;wBACJ,MAAM,CAAC,wBAAwB,EAC7B,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO,QAChD;wBACF,QAAQ;oBACV;iBACD;QACH;IACF,GACA,EAAE;IAGJ,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACnC,OACE,SACA,QACA;QAEA,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,QAAQ,MAAM,EAAE;QACtD,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,QAAQ;QACnC,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,eAAe;QAEjD,MAAM,aAAqB,QAAQ,GAAG,CAAC,CAAC,SAAW,CAAC;gBAClD,IAAI,CAAC,MAAM,EAAE,OAAO,KAAK,EAAE;gBAC3B,MAAM,CAAC,YAAY,EAAE,OAAO,KAAK,EAAE;gBACnC,QAAQ;YACV,CAAC;QAED,SAAS,CAAC,YAAc;mBACnB,UAAU,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;mBACjC;gBACH;oBAAE,IAAI;oBAAK,MAAM;oBAAyB,QAAQ;gBAAc;aACjE;QAED,MAAM,kBAAgC,EAAE;QAExC,KAAK,MAAM,cAAc,QAAS;YAChC,SAAS,CAAC,YACR,UAAU,GAAG,CAAC,CAAC,OACb,KAAK,EAAE,KAAK,CAAC,MAAM,EAAE,WAAW,KAAK,EAAE,GACnC;wBAAE,GAAG,IAAI;wBAAE,QAAQ;oBAAc,IACjC;YAIR,MAAM,OAAO,MAAM,2BACjB,WAAW,KAAK,EAChB,WAAW,WAAW,EACtB;YAGF,IAAI,MAAM;gBACR,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,WAAW,KAAK,CAAC,oBAAoB,CAAC;gBACtE,gBAAgB,IAAI,CAAC;oBAAE,GAAG,UAAU;oBAAE;gBAAK;YAC7C,OAAO;gBACL,QAAQ,IAAI,CAAC,CAAC,iCAAiC,EAAE,WAAW,KAAK,CAAC,WAAW,CAAC;YAChF;QACF;QAEA,SAAS,CAAC,YACR,UAAU,GAAG,CAAC,CAAC,OACb,KAAK,EAAE,KAAK,MAAM;oBAAE,GAAG,IAAI;oBAAE,QAAQ;gBAAY,IAAI;QAIzD,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,gBAAgB,MAAM,EAAE;QAC7D,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,QAAQ;QACnC,QAAQ,GAAG,CAAC,yBAAyB,gBAAgB,GAAG,CAAC,CAAA,IAAK,CAAC;gBAAE,OAAO,EAAE,KAAK;gBAAE,SAAS,CAAC,CAAC,EAAE,IAAI;YAAC,CAAC;QAEpG,iEAAiE;QACjE,IAAI,gBAAgB,MAAM,GAAG,KAAK,QAAQ;YACxC,QAAQ,GAAG,CAAC;YAEZ,iCAAiC;YACjC,0BAA0B;YAE1B,wBAAwB;YACxB,IAAI;gBACF,MAAM,uBAAuB,iBAAiB;YAChD,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,SAAS,CAAC,YACR,UAAU,GAAG,CAAC,CAAC,OACb,KAAK,EAAE,KAAK,MACR;4BAAE,GAAG,IAAI;4BAAE,QAAQ;4BAAU,MAAM;wBAAuC,IAC1E;YAGV;QACF,OAAO;YACL,QAAQ,IAAI,CAAC;YACb,QAAQ,IAAI,CAAC,CAAC,uBAAuB,EAAE,gBAAgB,MAAM,EAAE;YAC/D,QAAQ,IAAI,CAAC,CAAC,aAAa,EAAE,QAAQ;YACrC,QAAQ,IAAI,CAAC;QACf;IACF,GACA;QAAC;QAA4B;KAAuB;IAGtD,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACpC,OAAO;QACL,iDAAiD;QACjD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,EAAE,mBAAmB,EAAE,WAAW,EAAE,GAAG,MAAM,SAAS,IAAI;gBAEhE,IAAI,qBAAqB;oBACvB,MAAM,CAAC,sDAAsD,EAAE,YAAY,MAAM,CAAC,4DAA4D,CAAC;oBAC/I;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,0CAA0C;QAC5C;QAEA,gBAAgB;QAChB,oBAAoB;QACpB,kBAAkB,EAAE;QACpB,UAAU;QAEV,uCAAmC;;QAGnC;QAEA,MAAM,eAAuB;YAC3B;gBAAE,IAAI;gBAAK,MAAM;gBAAmB,QAAQ;YAAc;YAC1D;gBAAE,IAAI;gBAAK,MAAM;gBAAqB,QAAQ;YAAU;YACxD;gBAAE,IAAI;gBAAK,MAAM;gBAAyB,QAAQ;YAAU;SAC7D;QACD,SAAS;QAET,IAAI;YACF,SAAS,CAAC,YACR,UAAU,GAAG,CAAC,CAAC,OACb,KAAK,EAAE,KAAK,MAAM;wBAAE,GAAG,IAAI;wBAAE,QAAQ;oBAAc,IAAI;YAG3D,MAAM,wBAAwB;QAChC,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,SAAS,MAAM,IAAI,KAAK,cAAc;gBACzD,QAAQ,GAAG,CAAC;gBACZ;YACF;YAEA,SAAS,CAAC,YACR,UAAU,GAAG,CAAC,CAAC,OACb,KAAK,MAAM,KAAK,gBACZ;wBACE,GAAG,IAAI;wBACP,QAAQ;wBACR,MAAM,GAAG,KAAK,IAAI,CAAC,SAAS,EAC1B,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO,OACjD,CAAC,CAAC;oBACL,IACA;QAGV;IACF,GACA;QAAC;KAAwB;IAG3B,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACpC,0BAA0B;IAC5B,GAAG,EAAE;IAEL,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,0BAA0B;QAC1B,WAAW;YACT,gBAAgB;YAChB,oBAAoB;YACpB,kBAAkB,EAAE;YACpB,SAAS,EAAE;YACX,cAAc;YACd,UAAU;QACZ,GAAG;IACL,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,+IAAA,CAAA,UAAY;gBAEX,cAAc;gBACd,YAAY;eAFP;;;;;YAIN,kCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAoB,cAAc;4BAAc,OAAO;;;;;;sCAExD,8OAAC;4BACC,cAAc;4BACd,gBAAgB;;;;;;;;;;;;;;;;;0BAMxB,8OAAC,0JAAA,CAAA,UAAuB;gBAEtB,QAAQ;gBACR,SAAS;gBACT,mBAAmB;gBACnB,YAAY;gBACZ,eAAe;eALV;;;;;;;;;;;AASb", "debugId": null}}]}