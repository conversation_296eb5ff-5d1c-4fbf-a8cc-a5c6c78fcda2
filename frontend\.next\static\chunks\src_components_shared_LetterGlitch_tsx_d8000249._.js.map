{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/components/shared/LetterGlitch.tsx"], "sourcesContent": ["import { useRef, useEffect } from \"react\";\r\n\r\nconst LetterGlitch = ({\r\n  glitchColors = [\"#2b4539\", \"#61dca3\", \"#61b3dc\"],\r\n  glitchSpeed = 50,\r\n  centerVignette = false,\r\n  outerVignette = true,\r\n  smooth = true,\r\n}: {\r\n  glitchColors: string[];\r\n  glitchSpeed: number;\r\n  centerVignette: boolean;\r\n  outerVignette: boolean;\r\n  smooth: boolean;\r\n}) => {\r\n  const canvasRef = useRef<HTMLCanvasElement | null>(null);\r\n  const animationRef = useRef<number | null>(null);\r\n  const letters = useRef<\r\n    {\r\n      char: string;\r\n      color: string;\r\n      targetColor: string;\r\n      colorProgress: number;\r\n    }[]\r\n  >([]);\r\n  const grid = useRef({ columns: 0, rows: 0 });\r\n  const context = useRef<CanvasRenderingContext2D | null>(null);\r\n  const lastGlitchTime = useRef(Date.now());\r\n\r\n  const fontSize = 16;\r\n  const charWidth = 10;\r\n  const charHeight = 20;\r\n\r\n  const lettersAndSymbols = [\r\n    \"A\",\r\n    \"B\",\r\n    \"C\",\r\n    \"D\",\r\n    \"E\",\r\n    \"F\",\r\n    \"G\",\r\n    \"H\",\r\n    \"I\",\r\n    \"J\",\r\n    \"K\",\r\n    \"L\",\r\n    \"M\",\r\n    \"N\",\r\n    \"O\",\r\n    \"P\",\r\n    \"Q\",\r\n    \"R\",\r\n    \"S\",\r\n    \"T\",\r\n    \"U\",\r\n    \"V\",\r\n    \"W\",\r\n    \"X\",\r\n    \"Y\",\r\n    \"Z\",\r\n    \"!\",\r\n    \"@\",\r\n    \"#\",\r\n    \"$\",\r\n    \"&\",\r\n    \"*\",\r\n    \"(\",\r\n    \")\",\r\n    \"-\",\r\n    \"_\",\r\n    \"+\",\r\n    \"=\",\r\n    \"/\",\r\n    \"[\",\r\n    \"]\",\r\n    \"{\",\r\n    \"}\",\r\n    \";\",\r\n    \":\",\r\n    \"<\",\r\n    \">\",\r\n    \",\",\r\n    \"0\",\r\n    \"1\",\r\n    \"2\",\r\n    \"3\",\r\n    \"4\",\r\n    \"5\",\r\n    \"6\",\r\n    \"7\",\r\n    \"8\",\r\n    \"9\",\r\n  ];\r\n\r\n  const getRandomChar = () => {\r\n    return lettersAndSymbols[\r\n      Math.floor(Math.random() * lettersAndSymbols.length)\r\n    ];\r\n  };\r\n\r\n  const getRandomColor = () => {\r\n    return glitchColors[Math.floor(Math.random() * glitchColors.length)];\r\n  };\r\n\r\n  const hexToRgb = (hex: string) => {\r\n    const shorthandRegex = /^#?([a-f\\d])([a-f\\d])([a-f\\d])$/i;\r\n    hex = hex.replace(shorthandRegex, (m, r, g, b) => {\r\n      return r + r + g + g + b + b;\r\n    });\r\n\r\n    const result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(hex);\r\n    return result\r\n      ? {\r\n          r: parseInt(result[1], 16),\r\n          g: parseInt(result[2], 16),\r\n          b: parseInt(result[3], 16),\r\n        }\r\n      : null;\r\n  };\r\n\r\n  const interpolateColor = (\r\n    start: { r: number; g: number; b: number },\r\n    end: { r: number; g: number; b: number },\r\n    factor: number\r\n  ) => {\r\n    const result = {\r\n      r: Math.round(start.r + (end.r - start.r) * factor),\r\n      g: Math.round(start.g + (end.g - start.g) * factor),\r\n      b: Math.round(start.b + (end.b - start.b) * factor),\r\n    };\r\n    return `rgb(${result.r}, ${result.g}, ${result.b})`;\r\n  };\r\n\r\n  const calculateGrid = (width: number, height: number) => {\r\n    const columns = Math.ceil(width / charWidth);\r\n    const rows = Math.ceil(height / charHeight);\r\n    return { columns, rows };\r\n  };\r\n\r\n  const initializeLetters = (columns: number, rows: number) => {\r\n    grid.current = { columns, rows };\r\n    const totalLetters = columns * rows;\r\n    letters.current = Array.from({ length: totalLetters }, () => ({\r\n      char: getRandomChar(),\r\n      color: getRandomColor(),\r\n      targetColor: getRandomColor(),\r\n      colorProgress: 1,\r\n    }));\r\n  };\r\n\r\n  const resizeCanvas = () => {\r\n    const canvas = canvasRef.current;\r\n    if (!canvas) return;\r\n    const parent = canvas.parentElement;\r\n    if (!parent) return;\r\n\r\n    const dpr = window.devicePixelRatio || 1;\r\n    const rect = parent.getBoundingClientRect();\r\n\r\n    canvas.width = rect.width * dpr;\r\n    canvas.height = rect.height * dpr;\r\n\r\n    canvas.style.width = `${rect.width}px`;\r\n    canvas.style.height = `${rect.height}px`;\r\n\r\n    if (context.current) {\r\n      context.current.setTransform(dpr, 0, 0, dpr, 0, 0);\r\n    }\r\n\r\n    const { columns, rows } = calculateGrid(rect.width, rect.height);\r\n    initializeLetters(columns, rows);\r\n    drawLetters();\r\n  };\r\n\r\n  const drawLetters = () => {\r\n    if (!context.current || letters.current.length === 0) return;\r\n    const ctx = context.current;\r\n    const { width, height } = canvasRef.current!.getBoundingClientRect();\r\n    ctx.clearRect(0, 0, width, height);\r\n    ctx.font = `${fontSize}px monospace`;\r\n    ctx.textBaseline = \"top\";\r\n\r\n    letters.current.forEach((letter, index) => {\r\n      const x = (index % grid.current.columns) * charWidth;\r\n      const y = Math.floor(index / grid.current.columns) * charHeight;\r\n      ctx.fillStyle = letter.color;\r\n      ctx.fillText(letter.char, x, y);\r\n    });\r\n  };\r\n\r\n  const updateLetters = () => {\r\n    if (!letters.current || letters.current.length === 0) return;\r\n\r\n    const updateCount = Math.max(1, Math.floor(letters.current.length * 0.05));\r\n\r\n    for (let i = 0; i < updateCount; i++) {\r\n      const index = Math.floor(Math.random() * letters.current.length);\r\n      if (!letters.current[index]) continue;\r\n\r\n      letters.current[index].char = getRandomChar();\r\n      letters.current[index].targetColor = getRandomColor();\r\n\r\n      if (!smooth) {\r\n        letters.current[index].color = letters.current[index].targetColor;\r\n        letters.current[index].colorProgress = 1;\r\n      } else {\r\n        letters.current[index].colorProgress = 0;\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleSmoothTransitions = () => {\r\n    let needsRedraw = false;\r\n    letters.current.forEach((letter) => {\r\n      if (letter.colorProgress < 1) {\r\n        letter.colorProgress += 0.05;\r\n        if (letter.colorProgress > 1) letter.colorProgress = 1;\r\n\r\n        const startRgb = hexToRgb(letter.color);\r\n        const endRgb = hexToRgb(letter.targetColor);\r\n        if (startRgb && endRgb) {\r\n          letter.color = interpolateColor(\r\n            startRgb,\r\n            endRgb,\r\n            letter.colorProgress\r\n          );\r\n          needsRedraw = true;\r\n        }\r\n      }\r\n    });\r\n\r\n    if (needsRedraw) {\r\n      drawLetters();\r\n    }\r\n  };\r\n\r\n  const animate = () => {\r\n    const now = Date.now();\r\n    if (now - lastGlitchTime.current >= glitchSpeed) {\r\n      updateLetters();\r\n      drawLetters();\r\n      lastGlitchTime.current = now;\r\n    }\r\n\r\n    if (smooth) {\r\n      handleSmoothTransitions();\r\n    }\r\n\r\n    animationRef.current = requestAnimationFrame(animate);\r\n  };\r\n\r\n  useEffect(() => {\r\n    const canvas = canvasRef.current;\r\n    if (!canvas) return;\r\n\r\n    context.current = canvas.getContext(\"2d\");\r\n    resizeCanvas();\r\n    animate();\r\n\r\n    let resizeTimeout: NodeJS.Timeout;\r\n\r\n    const handleResize = () => {\r\n      clearTimeout(resizeTimeout);\r\n      resizeTimeout = setTimeout(() => {\r\n        cancelAnimationFrame(animationRef.current as number);\r\n        resizeCanvas();\r\n        animate();\r\n      }, 100);\r\n    };\r\n\r\n    window.addEventListener(\"resize\", handleResize);\r\n\r\n    return () => {\r\n      cancelAnimationFrame(animationRef.current!);\r\n      window.removeEventListener(\"resize\", handleResize);\r\n    };\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [glitchSpeed, smooth]);\r\n\r\n  return (\r\n    <div className=\"relative w-full h-full bg-background overflow-hidden\">\r\n      <canvas ref={canvasRef} className=\"block w-full h-full\" />\r\n      {outerVignette && (\r\n        <div className=\"absolute top-0 left-0 w-full h-full pointer-events-none bg-[radial-gradient(circle,_rgba(0,0,0,0)_60%,_rgba(0,0,0,1)_100%)]\"></div>\r\n      )}\r\n      {centerVignette && (\r\n        <div className=\"absolute top-0 left-0 w-full h-full pointer-events-none bg-[radial-gradient(circle,_rgba(0,0,0,0.98)_0%,_rgba(0,0,0,0.9)_50%,_rgba(0,0,0,0.89)_70%,_rgba(0,0,0,0.5)_90%)]\"></div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LetterGlitch;\r\n"], "names": [], "mappings": ";;;;AAAA;;;;AAEA,MAAM,eAAe,CAAC,EACpB,eAAe;IAAC;IAAW;IAAW;CAAU,EAChD,cAAc,EAAE,EAChB,iBAAiB,KAAK,EACtB,gBAAgB,IAAI,EACpB,SAAS,IAAI,EAOd;;IACC,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAA4B;IACnD,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAiB;IAC3C,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAOnB,EAAE;IACJ,MAAM,OAAO,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;QAAE,SAAS;QAAG,MAAM;IAAE;IAC1C,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAmC;IACxD,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,KAAK,GAAG;IAEtC,MAAM,WAAW;IACjB,MAAM,YAAY;IAClB,MAAM,aAAa;IAEnB,MAAM,oBAAoB;QACxB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,gBAAgB;QACpB,OAAO,iBAAiB,CACtB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,kBAAkB,MAAM,EACpD;IACH;IAEA,MAAM,iBAAiB;QACrB,OAAO,YAAY,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,aAAa,MAAM,EAAE;IACtE;IAEA,MAAM,WAAW,CAAC;QAChB,MAAM,iBAAiB;QACvB,MAAM,IAAI,OAAO,CAAC,gBAAgB,CAAC,GAAG,GAAG,GAAG;YAC1C,OAAO,IAAI,IAAI,IAAI,IAAI,IAAI;QAC7B;QAEA,MAAM,SAAS,4CAA4C,IAAI,CAAC;QAChE,OAAO,SACH;YACE,GAAG,SAAS,MAAM,CAAC,EAAE,EAAE;YACvB,GAAG,SAAS,MAAM,CAAC,EAAE,EAAE;YACvB,GAAG,SAAS,MAAM,CAAC,EAAE,EAAE;QACzB,IACA;IACN;IAEA,MAAM,mBAAmB,CACvB,OACA,KACA;QAEA,MAAM,SAAS;YACb,GAAG,KAAK,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI;YAC5C,GAAG,KAAK,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI;YAC5C,GAAG,KAAK,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI;QAC9C;QACA,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;IACrD;IAEA,MAAM,gBAAgB,CAAC,OAAe;QACpC,MAAM,UAAU,KAAK,IAAI,CAAC,QAAQ;QAClC,MAAM,OAAO,KAAK,IAAI,CAAC,SAAS;QAChC,OAAO;YAAE;YAAS;QAAK;IACzB;IAEA,MAAM,oBAAoB,CAAC,SAAiB;QAC1C,KAAK,OAAO,GAAG;YAAE;YAAS;QAAK;QAC/B,MAAM,eAAe,UAAU;QAC/B,QAAQ,OAAO,GAAG,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAa,GAAG,IAAM,CAAC;gBAC5D,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,eAAe;YACjB,CAAC;IACH;IAEA,MAAM,eAAe;QACnB,MAAM,SAAS,UAAU,OAAO;QAChC,IAAI,CAAC,QAAQ;QACb,MAAM,SAAS,OAAO,aAAa;QACnC,IAAI,CAAC,QAAQ;QAEb,MAAM,MAAM,OAAO,gBAAgB,IAAI;QACvC,MAAM,OAAO,OAAO,qBAAqB;QAEzC,OAAO,KAAK,GAAG,KAAK,KAAK,GAAG;QAC5B,OAAO,MAAM,GAAG,KAAK,MAAM,GAAG;QAE9B,OAAO,KAAK,CAAC,KAAK,GAAG,GAAG,KAAK,KAAK,CAAC,EAAE,CAAC;QACtC,OAAO,KAAK,CAAC,MAAM,GAAG,GAAG,KAAK,MAAM,CAAC,EAAE,CAAC;QAExC,IAAI,QAAQ,OAAO,EAAE;YACnB,QAAQ,OAAO,CAAC,YAAY,CAAC,KAAK,GAAG,GAAG,KAAK,GAAG;QAClD;QAEA,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,cAAc,KAAK,KAAK,EAAE,KAAK,MAAM;QAC/D,kBAAkB,SAAS;QAC3B;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,QAAQ,OAAO,IAAI,QAAQ,OAAO,CAAC,MAAM,KAAK,GAAG;QACtD,MAAM,MAAM,QAAQ,OAAO;QAC3B,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,UAAU,OAAO,CAAE,qBAAqB;QAClE,IAAI,SAAS,CAAC,GAAG,GAAG,OAAO;QAC3B,IAAI,IAAI,GAAG,GAAG,SAAS,YAAY,CAAC;QACpC,IAAI,YAAY,GAAG;QAEnB,QAAQ,OAAO,CAAC,OAAO,CAAC,CAAC,QAAQ;YAC/B,MAAM,IAAI,AAAC,QAAQ,KAAK,OAAO,CAAC,OAAO,GAAI;YAC3C,MAAM,IAAI,KAAK,KAAK,CAAC,QAAQ,KAAK,OAAO,CAAC,OAAO,IAAI;YACrD,IAAI,SAAS,GAAG,OAAO,KAAK;YAC5B,IAAI,QAAQ,CAAC,OAAO,IAAI,EAAE,GAAG;QAC/B;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,QAAQ,OAAO,IAAI,QAAQ,OAAO,CAAC,MAAM,KAAK,GAAG;QAEtD,MAAM,cAAc,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,CAAC,QAAQ,OAAO,CAAC,MAAM,GAAG;QAEpE,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,IAAK;YACpC,MAAM,QAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ,OAAO,CAAC,MAAM;YAC/D,IAAI,CAAC,QAAQ,OAAO,CAAC,MAAM,EAAE;YAE7B,QAAQ,OAAO,CAAC,MAAM,CAAC,IAAI,GAAG;YAC9B,QAAQ,OAAO,CAAC,MAAM,CAAC,WAAW,GAAG;YAErC,IAAI,CAAC,QAAQ;gBACX,QAAQ,OAAO,CAAC,MAAM,CAAC,KAAK,GAAG,QAAQ,OAAO,CAAC,MAAM,CAAC,WAAW;gBACjE,QAAQ,OAAO,CAAC,MAAM,CAAC,aAAa,GAAG;YACzC,OAAO;gBACL,QAAQ,OAAO,CAAC,MAAM,CAAC,aAAa,GAAG;YACzC;QACF;IACF;IAEA,MAAM,0BAA0B;QAC9B,IAAI,cAAc;QAClB,QAAQ,OAAO,CAAC,OAAO,CAAC,CAAC;YACvB,IAAI,OAAO,aAAa,GAAG,GAAG;gBAC5B,OAAO,aAAa,IAAI;gBACxB,IAAI,OAAO,aAAa,GAAG,GAAG,OAAO,aAAa,GAAG;gBAErD,MAAM,WAAW,SAAS,OAAO,KAAK;gBACtC,MAAM,SAAS,SAAS,OAAO,WAAW;gBAC1C,IAAI,YAAY,QAAQ;oBACtB,OAAO,KAAK,GAAG,iBACb,UACA,QACA,OAAO,aAAa;oBAEtB,cAAc;gBAChB;YACF;QACF;QAEA,IAAI,aAAa;YACf;QACF;IACF;IAEA,MAAM,UAAU;QACd,MAAM,MAAM,KAAK,GAAG;QACpB,IAAI,MAAM,eAAe,OAAO,IAAI,aAAa;YAC/C;YACA;YACA,eAAe,OAAO,GAAG;QAC3B;QAEA,IAAI,QAAQ;YACV;QACF;QAEA,aAAa,OAAO,GAAG,sBAAsB;IAC/C;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,SAAS,UAAU,OAAO;YAChC,IAAI,CAAC,QAAQ;YAEb,QAAQ,OAAO,GAAG,OAAO,UAAU,CAAC;YACpC;YACA;YAEA,IAAI;YAEJ,MAAM;uDAAe;oBACnB,aAAa;oBACb,gBAAgB;+DAAW;4BACzB,qBAAqB,aAAa,OAAO;4BACzC;4BACA;wBACF;8DAAG;gBACL;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAElC;0CAAO;oBACL,qBAAqB,aAAa,OAAO;oBACzC,OAAO,mBAAmB,CAAC,UAAU;gBACvC;;QACA,uDAAuD;QACzD;iCAAG;QAAC;QAAa;KAAO;IAExB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAO,KAAK;gBAAW,WAAU;;;;;;YACjC,+BACC,6LAAC;gBAAI,WAAU;;;;;;YAEhB,gCACC,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAIvB;GAhSM;KAAA;uCAkSS", "debugId": null}}]}