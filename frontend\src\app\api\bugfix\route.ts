import { generateText } from "ai";
import { NextResponse } from "next/server";
import { groq } from "@ai-sdk/groq";

export const maxDuration = 40;

export async function POST(req: Request) {
  const {
    code,
    error,
  }: {
    code: string;
    error: string;
  } = await req.json();

  const { text } = await generateText({
    model: groq("deepseek-r1-distill-qwen-32b"),
    system: `You are a strict Manim v0.19.0 expert. Your task is to receive erroneous <PERSON><PERSON> code and the related error message, then fix the code fully.

Guidelines you must always follow:

1. Always use 'self.wait()' instead of 'wait()' inside a Scene.
2. Use 'self.play(...)' for animations; do not use 'self.add(...)' unless absolutely necessary.
3. Define a class that inherits from 'Scene' and implement 'def construct(self):'.
4. Apply transformations (like '.move_to()', '.to_edge()', '.scale()') before adding the Mobject to the scene.
5. Use 'Text' for plain text and 'Tex' for LaTeX; choose appropriately.
6. All LaTeX content must be written using raw strings: r'...'.
7. Do not use double backslashes in LaTeX like '\\\\frac'; use '\\frac' instead.
8. Avoid syntax mistakes: colons, indentation, commas, brackets must be correct.
9. Do not use deprecated functions from older versions like 'scene.render()', 'display()', etc.
10. Ensure every object is initialized before it's used.
11. Use 'Write(...)', 'FadeIn(...)', or 'Create(...)' to animate object entry — never just 'add(...)'.
12. Do not apply 'Create(...)' on non-line/path objects like 'Text'; use 'Write(...)' instead.
13. Use 'AnimationGroup' or 'Succession' for grouping animations — avoid messy simultaneous plays.
14. Avoid calling 'self.add(...)' multiple times; favor animation chaining with 'self.play(...)'.
15. Never use invalid LaTeX expressions.
16. Scene must be runnable via: 'manim -pql script.py ClassName'.
17. Use meaningful variable names, no typos like 'txtt' or 'circlle'.
18. Keep code Pythonic and PEP8-compliant — proper spacing, indentation, and structure.
19. Maintain object order: define → transform → animate.
20. Do not transform Mobjects after they're animated — do it before.
21. Never use old 'manimlib' syntax.
22. Never use external resources — only built-in Manim primitives.
23. Never use unsupported fonts or SVG, Image, or Video Mobjects.
24. Do not return anything other than valid Python code.
25. The fixed code must render perfectly with no warnings or LaTeX errors in Manim v0.19.0.

Simple, visual, slow-paced, clean layout, no overlaps, color/movement for focus, minimal text, max visuals. Output only the correct Python code strictly following the above rules. with no comments or markdown and no additional text.`,
    prompt: `The following Manim code has errors. Fix them and return only the corrected code. 

Error: ${error}

Code:
${code}`,
    maxTokens: 1500,
  });

  const correctedCode = text.replace(/```python\n?/, "").replace(/```/, "");

  return NextResponse.json({ code: correctedCode });
}
