import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const { entryId, videoUrl, status } = await request.json();

    if (!entryId || !videoUrl || !status) {
      return NextResponse.json(
        { error: "Missing required parameters: entryId, videoUrl, status" },
        { status: 400 }
      );
    }

    // Log the completion notification
    console.log("Video processing completed:", {
      entryId,
      videoUrl,
      status,
      timestamp: new Date().toISOString(),
    });

    // Here you could add additional logic such as:
    // - Updating database records
    // - Sending notifications to users
    // - Triggering webhooks
    // - Updating cache

    return NextResponse.json(
      {
        success: true,
        message: "Video completion notification received",
        entryId,
        videoUrl,
        status,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error processing completion notification:", error);
    return NextResponse.json(
      { error: "Failed to process completion notification" },
      { status: 500 }
    );
  }
}
